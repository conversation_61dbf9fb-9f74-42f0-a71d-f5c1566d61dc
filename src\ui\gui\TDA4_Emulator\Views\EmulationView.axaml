<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="using:TDA4_Emulator.ViewModels"
             xmlns:converters="using:TDA4_Emulator.Converters"
             x:Class="TDA4_Emulator.Views.EmulationView"
             x:DataType="vm:EmulationViewModel">

    <UserControl.Resources>
        <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
    </UserControl.Resources>

    <Grid Margin="4">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Optimized Three-Column Header Layout -->
        <Grid Grid.Row="0" IsVisible="{Binding IsIPCMode}" Margin="0,0,0,4">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="4*"/>
                <ColumnDefinition Width="1"/>
                <ColumnDefinition Width="4*"/>
                <ColumnDefinition Width="1"/>
                <ColumnDefinition Width="2*"/>
            </Grid.ColumnDefinitions>

            <!-- Optimized Source Core Selection -->
            <StackPanel Grid.Column="0" Margin="8, 0, 0, 0">
                <TextBlock Text="Source Cores" FontSize="12" FontWeight="Bold"
                        Foreground="#800020" HorizontalAlignment="Center" Margin="0,0,0,4"/>
                <Border BorderBrush="#800020" BorderThickness="1"
                        CornerRadius="3" Padding="3" Background="White"
                        MinHeight="120">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                        <ItemsControl ItemsSource="{Binding SourceCores}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <UniformGrid Columns="5" HorizontalAlignment="Stretch"/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <CheckBox Content="{Binding DisplayName}"
                                            IsChecked="{Binding IsChecked, Mode=TwoWay}"
                                            IsEnabled="{Binding IsEnabled}"
                                            Margin="1" FontSize="9"
                                            Foreground="#333333"
                                            HorizontalAlignment="Left"
                                            VerticalAlignment="Center"/>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Border>
            </StackPanel>

            <!-- Optimized Destination Core Selection -->
            <StackPanel Grid.Column="2" Margin="8, 0, 0, 0">
                <TextBlock Text="Destination Cores" FontSize="12" FontWeight="Bold"
                        Foreground="#800020" HorizontalAlignment="Center" Margin="0,0,0,4"/>
                <Border BorderBrush="#800020" BorderThickness="1"
                        CornerRadius="3" Padding="3" Background="White"
                        MinHeight="120">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                        <ItemsControl ItemsSource="{Binding DestinationCores}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <UniformGrid Columns="5" HorizontalAlignment="Stretch"/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <CheckBox Content="{Binding DisplayName}"
                                            IsChecked="{Binding IsChecked, Mode=TwoWay}"
                                            IsEnabled="{Binding IsEnabled}"
                                            Margin="1" FontSize="9"
                                            Foreground="#333333"
                                            HorizontalAlignment="Left"
                                            VerticalAlignment="Center"/>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Border>
            </StackPanel>

            <!-- Compact Message Interface -->
            <StackPanel Grid.Column="4" Margin="8, 0, 0, 0">
                <TextBlock Text="Message Interface" FontSize="12" FontWeight="Bold"
                        Foreground="#800020" HorizontalAlignment="Center" Margin="0,0,0,4"/>
                <Border BorderBrush="#800020" BorderThickness="1"
                        CornerRadius="3" Padding="3" Background="White"
                        MinHeight="120">
                    <StackPanel Spacing="2" VerticalAlignment="Center">
                        <TextBox Text="{Binding Message}"
                                Watermark="Enter message..."
                                Height="26" TextWrapping="Wrap" AcceptsReturn="True"
                                FontSize="10" BorderThickness="1"
                                BorderBrush="#E0E0E0"
                                Background="White"/>
                        <Button Content="Send" Classes="primary"
                                Command="{Binding SendMessageCommand}"
                                IsEnabled="{Binding CanSendMessage}"
                                HorizontalAlignment="Stretch"
                                Padding="3,3"
                                FontSize="10"
                                FontWeight="Medium"
                                Background="#800020"
                                Foreground="White"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Grid>

        <!-- Compact GridSplitter -->
        <GridSplitter Grid.Row="1" Height="4" HorizontalAlignment="Stretch" VerticalAlignment="Center"
                      Background="#800020" ResizeDirection="Rows"/>

        <!-- Maximized Content Area -->
        <Grid Grid.Row="2">
            <!-- IPC Mode Content -->
            <Grid IsVisible="{Binding IsIPCMode}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Compact Terminal Control Header -->
                <Border Grid.Row="0" Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1"
                        Padding="8,4" Margin="0" CornerRadius="3,3,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                            <TextBlock Text="Communication Channel Terminals"
                                      FontSize="14" FontWeight="Bold"
                                      Foreground="#800020"/>
                            <TextBlock Text="• Real-time monitoring"
                                      FontSize="10" Foreground="#666666"
                                      Margin="8,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>

                        <Button Grid.Column="1" Content="Expand All" Classes="secondary"
                                Command="{Binding ExpandAllCommand}"
                                Margin="0,0,4,0" Padding="6,3"
                                FontSize="10"
                                Background="#E8E8E8" Foreground="#800020"
                                FontWeight="Medium"/>

                        <Button Grid.Column="2" Content="Collapse All" Classes="secondary"
                                Command="{Binding CollapseAllCommand}"
                                Margin="0,0,4,0" Padding="6,3"
                                FontSize="10"
                                Background="#E8E8E8" Foreground="#800020"
                                FontWeight="Medium"/>

                        <Button Grid.Column="3" Content="Clear All" Classes="secondary"
                                Command="{Binding ClearAllCommand}"
                                Padding="6,3"
                                FontSize="10"
                                Background="#E8E8E8" Foreground="#800020"
                                FontWeight="Medium"/>
                    </Grid>
                </Border>

                <!-- Enhanced Terminal Output Area with Proper Scrolling and Space-Efficient Layout -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto"
                            HorizontalScrollBarVisibility="Disabled"
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Background="#FAFAFA"
                            AllowAutoHide="True"
                            IsScrollChainingEnabled="True">
                    <ItemsControl ItemsSource="{Binding TerminalRows}"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Top"
                                Margin="2">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <!-- Vertical StackPanel for dynamic row heights -->
                                <StackPanel Orientation="Vertical" HorizontalAlignment="Stretch"/>
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <!-- Terminal Row with 3-column layout and dynamic height -->
                                <Grid HorizontalAlignment="Stretch"
                                      Height="{Binding RowHeight}"
                                      Margin="0,1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Terminal 1 -->
                                    <Border Grid.Column="0"
                                            BorderBrush="#800020" BorderThickness="1"
                                            CornerRadius="3" Background="White"
                                            Margin="2"
                                            HorizontalAlignment="Stretch"
                                            VerticalAlignment="Top"
                                            Effect="drop-shadow(0 1 4 #20000000)"
                                            IsVisible="{Binding Terminal1, Converter={x:Static ObjectConverters.IsNotNull}}">
                                        <Expander Header="{Binding Terminal1.DisplayName}"
                                                IsExpanded="{Binding Terminal1.IsExpanded, Mode=TwoWay}"
                                                HorizontalAlignment="Stretch"
                                                VerticalAlignment="Top"
                                                ExpandDirection="Down">
                                            <Expander.Header>
                                                <Border Background="#800020" CornerRadius="3,3,0,0"
                                                        Padding="6,2" Margin="-1,-1,-1,0">
                                                    <Grid HorizontalAlignment="Stretch">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>
                                                        <TextBlock Grid.Column="0" Text="{Binding Terminal1.DisplayName}"
                                                                VerticalAlignment="Center" FontWeight="Bold"
                                                                Foreground="White" FontSize="11"/>
                                                        <TextBlock Grid.Column="1" Text="●"
                                                                VerticalAlignment="Center"
                                                                Foreground="#90EE90" FontSize="9"
                                                                Margin="4,0,0,0"/>
                                                    </Grid>
                                                </Border>
                                            </Expander.Header>
                                            <!-- Terminal 1 Content -->
                                            <StackPanel Margin="2" Orientation="Vertical">
                                                <!-- Source Core Terminal -->
                                                <StackPanel Margin="0,0,0,2">
                                                    <Border Background="#800020" CornerRadius="3,3,0,0"
                                                            Padding="6,3" Margin="0">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="*"/>
                                                                <ColumnDefinition Width="Auto"/>
                                                            </Grid.ColumnDefinitions>
                                                            <TextBlock Grid.Column="0"
                                                                        Text="{Binding Terminal1.SourceCoreName}"
                                                                        FontWeight="Bold" FontSize="11"
                                                                        Foreground="White"
                                                                        VerticalAlignment="Center"/>
                                                            <Button Grid.Column="1" Content="Clear"
                                                                    Classes="secondary"
                                                                    Command="{Binding Terminal1.ClearSourceCommand}"
                                                                    Padding="4,2" FontSize="9"
                                                                    Background="White" Foreground="#800020"
                                                                    FontWeight="Medium"/>
                                                        </Grid>
                                                    </Border>
                                                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                                                HorizontalScrollBarVisibility="Disabled"
                                                                Height="200"
                                                                Background="White"
                                                                BorderBrush="#800020" BorderThickness="1,0,1,1"
                                                                CornerRadius="0,0,3,3">
                                                        <TextBox Classes="terminal"
                                                                Text="{Binding Terminal1.SourceTerminalOutput}"
                                                                IsReadOnly="True"
                                                                FontFamily="Consolas, Monaco, 'Courier New', monospace"
                                                                FontSize="9"
                                                                Background="White"
                                                                Foreground="#333333"
                                                                HorizontalAlignment="Stretch"
                                                                TextWrapping="Wrap"
                                                                AcceptsReturn="True"
                                                                BorderThickness="0"
                                                                Padding="4"/>
                                                    </ScrollViewer>
                                                </StackPanel>

                                                <!-- Destination Core Terminal -->
                                                <StackPanel>
                                                    <Border Background="#666666" CornerRadius="3,3,0,0"
                                                            Padding="6,3" Margin="0">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="*"/>
                                                                <ColumnDefinition Width="Auto"/>
                                                            </Grid.ColumnDefinitions>
                                                            <TextBlock Grid.Column="0"
                                                                        Text="{Binding Terminal1.DestinationCoreName}"
                                                                        FontWeight="Bold" FontSize="11"
                                                                        Foreground="White"
                                                                        VerticalAlignment="Center"/>
                                                            <Button Grid.Column="1" Content="Clear"
                                                                    Classes="secondary"
                                                                    Command="{Binding Terminal1.ClearDestinationCommand}"
                                                                    Padding="4,2" FontSize="9"
                                                                    Background="White" Foreground="#666666"
                                                                    FontWeight="Medium"/>
                                                        </Grid>
                                                    </Border>
                                                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                                                HorizontalScrollBarVisibility="Disabled"
                                                                Height="200"
                                                                Background="White"
                                                                BorderBrush="#666666" BorderThickness="1,0,1,1"
                                                                CornerRadius="0,0,3,3">
                                                        <TextBox Classes="terminal"
                                                                Text="{Binding Terminal1.DestinationTerminalOutput}"
                                                                IsReadOnly="True"
                                                                FontFamily="Consolas, Monaco, 'Courier New', monospace"
                                                                FontSize="9"
                                                                Background="White"
                                                                Foreground="#333333"
                                                                HorizontalAlignment="Stretch"
                                                                TextWrapping="Wrap"
                                                                AcceptsReturn="True"
                                                                BorderThickness="0"
                                                                Padding="4"/>
                                                    </ScrollViewer>
                                                </StackPanel>
                                            </StackPanel>
                                        </Expander>
                                    </Border>

                                    <!-- Terminal 2 -->
                                    <Border Grid.Column="1"
                                            BorderBrush="#800020" BorderThickness="1"
                                            CornerRadius="3" Background="White"
                                            Margin="2"
                                            HorizontalAlignment="Stretch"
                                            VerticalAlignment="Top"
                                            Effect="drop-shadow(0 1 4 #20000000)"
                                            IsVisible="{Binding Terminal2, Converter={x:Static ObjectConverters.IsNotNull}}">
                                        <Expander Header="{Binding Terminal2.DisplayName}"
                                                IsExpanded="{Binding Terminal2.IsExpanded, Mode=TwoWay}"
                                                HorizontalAlignment="Stretch"
                                                VerticalAlignment="Top"
                                                ExpandDirection="Down">
                                            <Expander.Header>
                                                <Border Background="#800020" CornerRadius="3,3,0,0"
                                                        Padding="6,2" Margin="-1,-1,-1,0">
                                                    <Grid HorizontalAlignment="Stretch">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>
                                                        <TextBlock Grid.Column="0" Text="{Binding Terminal2.DisplayName}"
                                                                VerticalAlignment="Center" FontWeight="Bold"
                                                                Foreground="White" FontSize="11"/>
                                                        <TextBlock Grid.Column="1" Text="●"
                                                                VerticalAlignment="Center"
                                                                Foreground="#90EE90" FontSize="9"
                                                                Margin="4,0,0,0"/>
                                                    </Grid>
                                                </Border>
                                            </Expander.Header>
                                            <!-- Terminal 2 Content -->
                                            <StackPanel Margin="2" Orientation="Vertical">
                                                <!-- Source Core Terminal -->
                                                <StackPanel Margin="0,0,0,2">
                                                    <Border Background="#800020" CornerRadius="3,3,0,0"
                                                            Padding="6,3" Margin="0">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="*"/>
                                                                <ColumnDefinition Width="Auto"/>
                                                            </Grid.ColumnDefinitions>
                                                            <TextBlock Grid.Column="0"
                                                                        Text="{Binding Terminal2.SourceCoreName}"
                                                                        FontWeight="Bold" FontSize="11"
                                                                        Foreground="White"
                                                                        VerticalAlignment="Center"/>
                                                            <Button Grid.Column="1" Content="Clear"
                                                                    Classes="secondary"
                                                                    Command="{Binding Terminal2.ClearSourceCommand}"
                                                                    Padding="4,2" FontSize="9"
                                                                    Background="White" Foreground="#800020"
                                                                    FontWeight="Medium"/>
                                                        </Grid>
                                                    </Border>
                                                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                                                HorizontalScrollBarVisibility="Disabled"
                                                                Height="200"
                                                                Background="White"
                                                                BorderBrush="#800020" BorderThickness="1,0,1,1"
                                                                CornerRadius="0,0,3,3">
                                                        <TextBox Classes="terminal"
                                                                Text="{Binding Terminal2.SourceTerminalOutput}"
                                                                IsReadOnly="True"
                                                                FontFamily="Consolas, Monaco, 'Courier New', monospace"
                                                                FontSize="9"
                                                                Background="White"
                                                                Foreground="#333333"
                                                                HorizontalAlignment="Stretch"
                                                                TextWrapping="Wrap"
                                                                AcceptsReturn="True"
                                                                BorderThickness="0"
                                                                Padding="4"/>
                                                    </ScrollViewer>
                                                </StackPanel>

                                                <!-- Destination Core Terminal -->
                                                <StackPanel>
                                                    <Border Background="#666666" CornerRadius="3,3,0,0"
                                                            Padding="6,3" Margin="0">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="*"/>
                                                                <ColumnDefinition Width="Auto"/>
                                                            </Grid.ColumnDefinitions>
                                                            <TextBlock Grid.Column="0"
                                                                        Text="{Binding Terminal2.DestinationCoreName}"
                                                                        FontWeight="Bold" FontSize="11"
                                                                        Foreground="White"
                                                                        VerticalAlignment="Center"/>
                                                            <Button Grid.Column="1" Content="Clear"
                                                                    Classes="secondary"
                                                                    Command="{Binding Terminal2.ClearDestinationCommand}"
                                                                    Padding="4,2" FontSize="9"
                                                                    Background="White" Foreground="#666666"
                                                                    FontWeight="Medium"/>
                                                        </Grid>
                                                    </Border>
                                                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                                                HorizontalScrollBarVisibility="Disabled"
                                                                Height="200"
                                                                Background="White"
                                                                BorderBrush="#666666" BorderThickness="1,0,1,1"
                                                                CornerRadius="0,0,3,3">
                                                        <TextBox Classes="terminal"
                                                                Text="{Binding Terminal2.DestinationTerminalOutput}"
                                                                IsReadOnly="True"
                                                                FontFamily="Consolas, Monaco, 'Courier New', monospace"
                                                                FontSize="9"
                                                                Background="White"
                                                                Foreground="#333333"
                                                                HorizontalAlignment="Stretch"
                                                                TextWrapping="Wrap"
                                                                AcceptsReturn="True"
                                                                BorderThickness="0"
                                                                Padding="4"/>
                                                    </ScrollViewer>
                                                </StackPanel>
                                            </StackPanel>
                                        </Expander>
                                    </Border>

                                    <!-- Terminal 3 -->
                                    <Border Grid.Column="2"
                                            BorderBrush="#800020" BorderThickness="1"
                                            CornerRadius="3" Background="White"
                                            Margin="2"
                                            HorizontalAlignment="Stretch"
                                            VerticalAlignment="Top"
                                            Effect="drop-shadow(0 1 4 #20000000)"
                                            IsVisible="{Binding Terminal3, Converter={x:Static ObjectConverters.IsNotNull}}">
                                        <Expander Header="{Binding Terminal3.DisplayName}"
                                                IsExpanded="{Binding Terminal3.IsExpanded, Mode=TwoWay}"
                                                HorizontalAlignment="Stretch"
                                                VerticalAlignment="Top"
                                                ExpandDirection="Down">
                                            <Expander.Header>
                                                <Border Background="#800020" CornerRadius="3,3,0,0"
                                                        Padding="6,2" Margin="-1,-1,-1,0">
                                                    <Grid HorizontalAlignment="Stretch">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>
                                                        <TextBlock Grid.Column="0" Text="{Binding Terminal3.DisplayName}"
                                                                VerticalAlignment="Center" FontWeight="Bold"
                                                                Foreground="White" FontSize="11"/>
                                                        <TextBlock Grid.Column="1" Text="●"
                                                                VerticalAlignment="Center"
                                                                Foreground="#90EE90" FontSize="9"
                                                                Margin="4,0,0,0"/>
                                                    </Grid>
                                                </Border>
                                            </Expander.Header>
                                            <!-- Terminal 3 Content -->
                                            <StackPanel Margin="2" Orientation="Vertical">
                                                <!-- Source Core Terminal -->
                                                <StackPanel Margin="0,0,0,2">
                                                    <Border Background="#800020" CornerRadius="3,3,0,0"
                                                            Padding="6,3" Margin="0">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="*"/>
                                                                <ColumnDefinition Width="Auto"/>
                                                            </Grid.ColumnDefinitions>
                                                            <TextBlock Grid.Column="0"
                                                                        Text="{Binding Terminal3.SourceCoreName}"
                                                                        FontWeight="Bold" FontSize="11"
                                                                        Foreground="White"
                                                                        VerticalAlignment="Center"/>
                                                            <Button Grid.Column="1" Content="Clear"
                                                                    Classes="secondary"
                                                                    Command="{Binding Terminal3.ClearSourceCommand}"
                                                                    Padding="4,2" FontSize="9"
                                                                    Background="White" Foreground="#800020"
                                                                    FontWeight="Medium"/>
                                                        </Grid>
                                                    </Border>
                                                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                                                HorizontalScrollBarVisibility="Disabled"
                                                                Height="200"
                                                                Background="White"
                                                                BorderBrush="#800020" BorderThickness="1,0,1,1"
                                                                CornerRadius="0,0,3,3">
                                                        <TextBox Classes="terminal"
                                                                Text="{Binding Terminal3.SourceTerminalOutput}"
                                                                IsReadOnly="True"
                                                                FontFamily="Consolas, Monaco, 'Courier New', monospace"
                                                                FontSize="9"
                                                                Background="White"
                                                                Foreground="#333333"
                                                                HorizontalAlignment="Stretch"
                                                                TextWrapping="Wrap"
                                                                AcceptsReturn="True"
                                                                BorderThickness="0"
                                                                Padding="4"/>
                                                    </ScrollViewer>
                                                </StackPanel>

                                                <!-- Destination Core Terminal -->
                                                <StackPanel>
                                                    <Border Background="#666666" CornerRadius="3,3,0,0"
                                                            Padding="6,3" Margin="0">
                                                        <Grid>
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="*"/>
                                                                <ColumnDefinition Width="Auto"/>
                                                            </Grid.ColumnDefinitions>
                                                            <TextBlock Grid.Column="0"
                                                                        Text="{Binding Terminal3.DestinationCoreName}"
                                                                        FontWeight="Bold" FontSize="11"
                                                                        Foreground="White"
                                                                        VerticalAlignment="Center"/>
                                                            <Button Grid.Column="1" Content="Clear"
                                                                    Classes="secondary"
                                                                    Command="{Binding Terminal3.ClearDestinationCommand}"
                                                                    Padding="4,2" FontSize="9"
                                                                    Background="White" Foreground="#666666"
                                                                    FontWeight="Medium"/>
                                                        </Grid>
                                                    </Border>
                                                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                                                HorizontalScrollBarVisibility="Disabled"
                                                                Height="200"
                                                                Background="White"
                                                                BorderBrush="#666666" BorderThickness="1,0,1,1"
                                                                CornerRadius="0,0,3,3">
                                                        <TextBox Classes="terminal"
                                                                Text="{Binding Terminal3.DestinationTerminalOutput}"
                                                                IsReadOnly="True"
                                                                FontFamily="Consolas, Monaco, 'Courier New', monospace"
                                                                FontSize="9"
                                                                Background="White"
                                                                Foreground="#333333"
                                                                HorizontalAlignment="Stretch"
                                                                TextWrapping="Wrap"
                                                                AcceptsReturn="True"
                                                                BorderThickness="0"
                                                                Padding="4"/>
                                                    </ScrollViewer>
                                                </StackPanel>
                                            </StackPanel>
                                        </Expander>
                                    </Border>
                                </Grid>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>
            </Grid>

            <!-- TestMode1 Content -->
            <Grid IsVisible="{Binding IsTestMode1}">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Spacing="16">
                    <TextBlock Text="Test Mode 1" FontSize="24" FontWeight="Bold"
                               Foreground="#800020" HorizontalAlignment="Center"/>
                    <TextBlock Text="This test mode is reserved for future functionality."
                               FontSize="14" Foreground="#666666" HorizontalAlignment="Center"/>
                    <TextBlock Text="Placeholder interface for specialized testing scenarios."
                               FontSize="12" Foreground="#999999" HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>

            <!-- TestMode2 Content -->
            <Grid IsVisible="{Binding IsTestMode2}">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Spacing="16">
                    <TextBlock Text="Test Mode 2" FontSize="24" FontWeight="Bold"
                               Foreground="#800020" HorizontalAlignment="Center"/>
                    <TextBlock Text="This test mode is reserved for future functionality."
                               FontSize="14" Foreground="#666666" HorizontalAlignment="Center"/>
                    <TextBlock Text="Placeholder interface for specialized testing scenarios."
                               FontSize="12" Foreground="#999999" HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Grid>

        <!-- Navigation -->
        <Grid Grid.Row="3" Margin="0,4,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Back Button -->
            <Button Grid.Column="0" Content="Back" Classes="secondary"
                    Command="{Binding BackCommand}"
                    MinWidth="100" Padding="16,8"/>

            <!-- Stop Emulation Button -->
            <Button Grid.Column="2" Content="Stop Emulation" Classes="primary"
                    Command="{Binding StopEmulationCommand}"
                    IsEnabled="{Binding IsEmulationRunning}"
                    MinWidth="120" Padding="16,8"
                    Margin="0,0,8,0"/>

            <!-- Start Emulation Button -->
            <Button Grid.Column="3" Content="Start Emulation" Classes="primary"
                    Command="{Binding StartEmulationCommand}"
                    IsEnabled="{Binding CanStartEmulation}"
                    MinWidth="120" Padding="16,8"/>
        </Grid>
    </Grid>

</UserControl>
