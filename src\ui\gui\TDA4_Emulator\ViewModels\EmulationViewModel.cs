using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reactive;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using System.Text;
using System.Threading.Tasks;
using ReactiveUI;
using TDA4_Emulator.Models;
using TDA4_Emulator.Services;

namespace TDA4_Emulator.ViewModels;

/// <summary>
/// ViewModel for Step 3: Emulation
/// </summary>
public class EmulationViewModel : ViewModelBase
{
    private readonly LoggingService _logger;
    private readonly WizardNavigationService _navigationService;
    private readonly ProcessManager _processManager;

    private bool _isEmulationRunning;
    private string _message = string.Empty;
    private string _selectedCommunicationChannel = string.Empty;
    private bool _showAllTerminals;
    private ApplicationConfiguration? _configuration;
    private TestMode _currentTestMode = TestMode.IPC;

    public EmulationViewModel(
        LoggingService logger,
        WizardNavigationService navigationService,
        ProcessManager processManager)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _navigationService = navigationService ?? throw new ArgumentNullException(nameof(navigationService));
        _processManager = processManager ?? throw new ArgumentNullException(nameof(processManager));

        // Initialize collections
        SourceCores = new ObservableCollection<CheckboxItemViewModel>();
        DestinationCores = new ObservableCollection<CheckboxItemViewModel>();
        CommunicationChannels = new ObservableCollection<CommunicationChannelExpander>();
        CoreTerminals = new ObservableCollection<CoreTerminalExpander>();
        TerminalOutput = new ObservableCollection<TerminalLine>();
        TerminalRows = new ObservableCollection<TerminalRow>();

        // Initialize commands
        BackCommand = ReactiveCommand.Create(NavigateBack);
        StartEmulationCommand = ReactiveCommand.CreateFromTask(StartEmulationAsync, 
            this.WhenAnyValue(x => x.CanStartEmulation));
        StopEmulationCommand = ReactiveCommand.CreateFromTask(StopEmulationAsync,
            this.WhenAnyValue(x => x.IsEmulationRunning));
        SendMessageCommand = ReactiveCommand.CreateFromTask(SendMessageAsync,
            this.WhenAnyValue(x => x.CanSendMessage));
        SelectChannelCommand = ReactiveCommand.Create<CommunicationChannelExpander>(SelectChannel);
        ExpandAllCommand = ReactiveCommand.Create(ExpandAll);
        CollapseAllCommand = ReactiveCommand.Create(CollapseAll);
        ClearAllCommand = ReactiveCommand.Create(ClearAll);

        // Set up property change handlers
        this.WhenAnyValue(x => x.Message)
            .Subscribe(_ => this.RaisePropertyChanged(nameof(CanSendMessage)));

        // Subscribe to process manager events
        _processManager.OutputReceived += OnOutputReceived;
        _processManager.ProcessStatusChanged += OnProcessStatusChanged;

        _logger.LogInfo("EmulationViewModel initialized");
    }

    #region Properties

    /// <summary>
    /// Source core selection items
    /// </summary>
    public ObservableCollection<CheckboxItemViewModel> SourceCores { get; }

    /// <summary>
    /// Destination core selection items
    /// </summary>
    public ObservableCollection<CheckboxItemViewModel> DestinationCores { get; }

    /// <summary>
    /// Active communication channels
    /// </summary>
    public ObservableCollection<CommunicationChannelExpander> CommunicationChannels { get; }

    /// <summary>
    /// Core terminal expanders for individual core outputs
    /// </summary>
    public ObservableCollection<CoreTerminalExpander> CoreTerminals { get; }

    /// <summary>
    /// Terminal output lines
    /// </summary>
    public ObservableCollection<TerminalLine> TerminalOutput { get; }

    /// <summary>
    /// Collection of terminal rows for 3-column layout with dynamic height
    /// </summary>
    public ObservableCollection<TerminalRow> TerminalRows { get; }

    /// <summary>
    /// Indicates if emulation is currently running
    /// </summary>
    public bool IsEmulationRunning
    {
        get => _isEmulationRunning;
        private set
        {
            this.RaiseAndSetIfChanged(ref _isEmulationRunning, value);
            this.RaisePropertyChanged(nameof(CanStartEmulation));
        }
    }

    /// <summary>
    /// Message to send between cores
    /// </summary>
    public string Message
    {
        get => _message;
        set => this.RaiseAndSetIfChanged(ref _message, value);
    }

    /// <summary>
    /// Currently selected communication channel
    /// </summary>
    public string SelectedCommunicationChannel
    {
        get => _selectedCommunicationChannel;
        set
        {
            this.RaiseAndSetIfChanged(ref _selectedCommunicationChannel, value);
            UpdateTerminalOutput();
        }
    }

    /// <summary>
    /// Indicates if showing all terminals
    /// </summary>
    public bool ShowAllTerminals
    {
        get => _showAllTerminals;
        private set
        {
            this.RaiseAndSetIfChanged(ref _showAllTerminals, value);
            UpdateTerminalOutput();
        }
    }

    /// <summary>
    /// Selected source cores
    /// </summary>
    public IEnumerable<CoreType> SelectedSourceCores =>
        SourceCores.Where(c => c.IsChecked).Select(c => c.CoreType);

    /// <summary>
    /// Selected destination cores
    /// </summary>
    public IEnumerable<CoreType> SelectedDestinationCores =>
        DestinationCores.Where(c => c.IsChecked).Select(c => c.CoreType);

    /// <summary>
    /// Determines if emulation can be started
    /// </summary>
    public bool CanStartEmulation => !IsEmulationRunning && HasValidConfiguration();

    /// <summary>
    /// Determines if a message can be sent
    /// </summary>
    public bool CanSendMessage => IsEmulationRunning &&
        SelectedSourceCores.Any() &&
        SelectedDestinationCores.Any() &&
        !string.IsNullOrWhiteSpace(Message);

    /// <summary>
    /// Terminal output as formatted text
    /// </summary>
    public string TerminalOutputText
    {
        get
        {
            var sb = new StringBuilder();
            foreach (var line in TerminalOutput)
            {
                if (line != null && !string.IsNullOrEmpty(line.FormattedText))
                {
                    sb.AppendLine(line.FormattedText);
                }
            }
            return sb.ToString();
        }
    }

    /// <summary>
    /// Current test mode
    /// </summary>
    public TestMode CurrentTestMode
    {
        get => _currentTestMode;
        private set => this.RaiseAndSetIfChanged(ref _currentTestMode, value);
    }

    /// <summary>
    /// Indicates if the current test mode is IPC
    /// </summary>
    public bool IsIPCMode => CurrentTestMode == TestMode.IPC;

    /// <summary>
    /// Indicates if the current test mode is TestMode1
    /// </summary>
    public bool IsTestMode1 => CurrentTestMode == TestMode.TestMode1;

    /// <summary>
    /// Indicates if the current test mode is TestMode2
    /// </summary>
    public bool IsTestMode2 => CurrentTestMode == TestMode.TestMode2;

    #endregion

    #region Commands

    /// <summary>
    /// Command to navigate to the previous step
    /// </summary>
    public ReactiveCommand<Unit, Unit> BackCommand { get; }

    /// <summary>
    /// Command to start emulation
    /// </summary>
    public ReactiveCommand<Unit, Unit> StartEmulationCommand { get; }

    /// <summary>
    /// Command to stop emulation
    /// </summary>
    public ReactiveCommand<Unit, Unit> StopEmulationCommand { get; }

    /// <summary>
    /// Command to send message
    /// </summary>
    public ReactiveCommand<Unit, Unit> SendMessageCommand { get; }

    /// <summary>
    /// Command to select a communication channel
    /// </summary>
    public ReactiveCommand<CommunicationChannelExpander, Unit> SelectChannelCommand { get; }

    /// <summary>
    /// Command to expand all expanders (channels and terminals)
    /// </summary>
    public ReactiveCommand<Unit, Unit> ExpandAllCommand { get; }

    /// <summary>
    /// Command to collapse all expanders (channels and terminals)
    /// </summary>
    public ReactiveCommand<Unit, Unit> CollapseAllCommand { get; }

    /// <summary>
    /// Command to clear all terminals and channels
    /// </summary>
    public ReactiveCommand<Unit, Unit> ClearAllCommand { get; }

    #endregion

    #region Private Methods

    /// <summary>
    /// Initializes core selection items from configuration with only cores that have valid binary paths
    /// </summary>
    private void InitializeCoreSelectionsFromConfiguration()
    {
        if (_configuration == null) return;

        // Clear existing selections
        SourceCores.Clear();
        DestinationCores.Clear();

        // Get cores with valid binary paths
        var validCores = GetCoresWithValidBinaryPaths();

        if (validCores.Any())
        {
            // Initialize source cores with ReactiveUI pattern
            InitializeCoreCollection(SourceCores, validCores, true);

            // Initialize destination cores with ReactiveUI pattern
            InitializeCoreCollection(DestinationCores, validCores, false);
        }

        _logger.LogInfo($"Initialized core selections with {validCores.Count()} valid cores");
    }

    /// <summary>
    /// Initializes a core collection with ReactiveUI pattern following the provided example
    /// </summary>
    private void InitializeCoreCollection(ObservableCollection<CheckboxItemViewModel> collection, IEnumerable<CoreType> validCores, bool isSource)
    {
        // Add "Select All" checkbox first
        var selectAllItem = new CheckboxItemViewModel
        {
            DisplayName = "Select All",
            IsAllItem = true,
            IsChecked = false,
            CoreType = CoreType.All
        };
        collection.Add(selectAllItem);

        // Add individual core checkboxes
        foreach (var coreType in validCores)
        {
            var coreItem = new CheckboxItemViewModel
            {
                DisplayName = coreType.GetDisplayName(),
                IsAllItem = false,
                IsChecked = false,
                CoreType = coreType
            };
            collection.Add(coreItem);

            // Set up reactive subscription for individual checkbox changes
            coreItem.WhenAnyValue(x => x.IsChecked)
                .Subscribe(isChecked =>
                {
                    _logger.LogInfo($"Individual checkbox {coreItem.DisplayName} is {(isChecked ? "checked" : "unchecked")} for {(isSource ? "source" : "destination")} cores");

                    // Update communication channels when individual selection changes
                    UpdateCommunicationChannels();
                });
        }

        // Set up reactive subscription for "Select All" behavior
        selectAllItem.WhenAnyValue(x => x.IsChecked)
            .Subscribe(isAllChecked =>
            {
                _logger.LogInfo($"Select All checkbox is {(isAllChecked ? "checked" : "unchecked")} for {(isSource ? "source" : "destination")} cores");

                // Update IsEnabled for non-"All" items
                foreach (var item in collection.Where(x => !x.IsAllItem))
                {
                    item.IsEnabled = !isAllChecked;
                }

                // Update communication channels when selection changes
                UpdateCommunicationChannels();
            });
    }

    /// <summary>
    /// Gets cores that have valid binary paths configured
    /// </summary>
    private IEnumerable<CoreType> GetCoresWithValidBinaryPaths()
    {
        if (_configuration == null) return Enumerable.Empty<CoreType>();

        var validCores = new List<CoreType>();

        foreach (var coreType in CoreTypeExtensions.GetIndividualCores())
        {
            var binaryPath = _configuration.CoreBinaryPaths.GetPath(coreType);
            if (!string.IsNullOrWhiteSpace(binaryPath) && System.IO.File.Exists(binaryPath))
            {
                validCores.Add(coreType);
            }
        }

        return validCores;
    }

    /// <summary>
    /// Initializes core terminal expanders for cores with valid binary paths
    /// </summary>
    private void InitializeCoreTerminals()
    {
        CoreTerminals.Clear();

        var validCores = GetCoresWithValidBinaryPaths();
        foreach (var coreType in validCores)
        {
            var terminal = new CoreTerminalExpander(coreType);
            terminal.AppendOutput($"[SYSTEM] Terminal initialized for {coreType.GetDisplayName()}");
            terminal.AppendOutput($"[SYSTEM] Binary path: {_configuration?.CoreBinaryPaths.GetPath(coreType)}");
            CoreTerminals.Add(terminal);
        }

        _logger.LogInfo($"Initialized {CoreTerminals.Count} core terminals for cores with valid binary paths");

        // If no valid cores found, add a message to help the user
        if (!validCores.Any())
        {
            _logger.LogWarning("No cores with valid binary paths found. Please configure binary paths in Step 2.");
        }
    }

    /// <summary>
    /// Updates communication channels based on selected cores
    /// </summary>
    public void UpdateCommunicationChannels()
    {
        _logger.LogInfo("Updating communication channels based on selected cores");
        CommunicationChannels.Clear();

        var sourceCores = SelectedSourceCores.ToList();
        var destCores = SelectedDestinationCores.ToList();

        _logger.LogInfo($"Initial selection - Source cores: [{string.Join(", ", sourceCores.Select(c => c.GetDisplayName()))}]");
        _logger.LogInfo($"Initial selection - Destination cores: [{string.Join(", ", destCores.Select(c => c.GetDisplayName()))}]");

        // Get cores with valid binary paths for "All" expansion
        var validCores = GetCoresWithValidBinaryPaths().ToList();

        // Expand "All" selections to only include cores with valid binary paths
        if (sourceCores.Contains(CoreType.All))
        {
            sourceCores = validCores;
            _logger.LogInfo($"Expanded 'All' source cores to: [{string.Join(", ", sourceCores.Select(c => c.GetDisplayName()))}]");
        }

        if (destCores.Contains(CoreType.All))
        {
            destCores = validCores;
            _logger.LogInfo($"Expanded 'All' destination cores to: [{string.Join(", ", destCores.Select(c => c.GetDisplayName()))}]");
        }

        // Remove CoreType.All from individual selections (it shouldn't be there for individual cores)
        sourceCores = sourceCores.Where(c => c != CoreType.All).ToList();
        destCores = destCores.Where(c => c != CoreType.All).ToList();

        _logger.LogInfo($"Final source cores: [{string.Join(", ", sourceCores.Select(c => c.GetDisplayName()))}]");
        _logger.LogInfo($"Final destination cores: [{string.Join(", ", destCores.Select(c => c.GetDisplayName()))}]");

        // Create communication channels only for cores with valid binary paths
        foreach (var source in sourceCores)
        {
            foreach (var dest in destCores)
            {
                if (source != dest) // Don't create self-communication
                {
                    // Double-check that both cores have valid binary paths
                    var sourceBinaryPath = _configuration?.CoreBinaryPaths.GetPath(source);
                    var destBinaryPath = _configuration?.CoreBinaryPaths.GetPath(dest);

                    if (!string.IsNullOrWhiteSpace(sourceBinaryPath) && System.IO.File.Exists(sourceBinaryPath) &&
                        !string.IsNullOrWhiteSpace(destBinaryPath) && System.IO.File.Exists(destBinaryPath))
                    {
                        var channel = new CommunicationChannelExpander(source, dest);
                        CommunicationChannels.Add(channel);
                        _logger.LogInfo($"Created communication channel: {source.GetDisplayName()} → {dest.GetDisplayName()}");
                    }
                    else
                    {
                        _logger.LogInfo($"Skipped channel {source.GetDisplayName()} → {dest.GetDisplayName()} - missing binary paths");
                    }
                }
            }
        }

        _logger.LogInfo($"Updated communication channels: {CommunicationChannels.Count} channels (only cores with valid binary paths)");

        // Update terminal rows for 3-column layout
        UpdateTerminalRows();
    }

    /// <summary>
    /// Checks if there's a valid configuration for emulation
    /// </summary>
    private bool HasValidConfiguration()
    {
        // Check if we have at least one core with a valid binary path
        var validCores = GetCoresWithValidBinaryPaths();
        return validCores.Any();
    }

    /// <summary>
    /// Updates terminal rows for 3-column layout with dynamic height
    /// </summary>
    private void UpdateTerminalRows()
    {
        TerminalRows.Clear();

        var channels = CommunicationChannels.ToList();

        _logger.LogInfo($"UpdateTerminalRows: Starting with {channels.Count} channels");

        // Group channels into rows of 3
        for (int i = 0; i < channels.Count; i += 3)
        {
            var row = new TerminalRow
            {
                Terminal1 = i < channels.Count ? channels[i] : null,
                Terminal2 = i + 1 < channels.Count ? channels[i + 1] : null,
                Terminal3 = i + 2 < channels.Count ? channels[i + 2] : null
            };

            TerminalRows.Add(row);

            _logger.LogInfo($"Created row {TerminalRows.Count}: T1={row.Terminal1?.DisplayName}, T2={row.Terminal2?.DisplayName}, T3={row.Terminal3?.DisplayName}, InitialHeight={row.RowHeight}");
        }

        _logger.LogInfo($"Updated terminal rows: {TerminalRows.Count} rows for {channels.Count} channels");
    }

    /// <summary>
    /// Updates terminal output based on selected channel or show all
    /// </summary>
    private void UpdateTerminalOutput()
    {
        // This would filter terminal output based on selected communication channel
        // Implementation depends on how terminal output is structured
        _logger.LogInfo($"Updated terminal output for channel: {SelectedCommunicationChannel}, ShowAll: {ShowAllTerminals}");
    }

    /// <summary>
    /// Navigates to the previous step
    /// </summary>
    private void NavigateBack()
    {
        _logger.LogInfo("Navigating back from emulation step");

        if (!_navigationService.NavigatePrevious())
        {
            _logger.LogError("Failed to navigate to previous step");
        }
    }

    /// <summary>
    /// Starts emulation with enhanced boot sequence and improved error handling
    /// </summary>
    private async Task StartEmulationAsync()
    {
        try
        {
            _logger.LogInfo("Starting emulation with enhanced boot sequence");

            if (_configuration == null)
            {
                _logger.LogError("No configuration available for emulation");
                return;
            }

            // Validate that we have at least one core with a valid binary path
            var validCores = GetCoresWithValidBinaryPaths();
            if (!validCores.Any())
            {
                _logger.LogError("No cores have valid binary paths configured. Cannot start emulation.");
                _logger.LogInfo("Please configure binary paths in Step 2 before starting emulation.");

                // Update all core terminals with error message
                foreach (var terminal in CoreTerminals)
                {
                    terminal.AppendOutput("[ERROR] No valid binary path configured for this core");
                    terminal.AppendOutput("Please configure binary paths in Step 2 (Configuration)");
                }
                return;
            }

            _logger.LogInfo($"Found {validCores.Count()} cores with valid binary paths");

            // Step 1: Launch QEMU with ARM cores
            await LaunchQemuWithArmCores();

            // Step 2: Start IVSHMEM Server
            await StartIvshmemServer();

            // Step 3: Launch DSP cores and R5F cores
            await LaunchDspCores();

            IsEmulationRunning = _processManager.IsAnyProcessRunning;

            if (IsEmulationRunning)
            {
                _logger.LogInfo("Emulation boot sequence completed successfully");
            }
            else
            {
                _logger.LogWarning("Emulation boot sequence completed but no processes are running");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError("Error during emulation boot sequence", ex);
            IsEmulationRunning = false;

            // Update all core terminals with error message
            foreach (var terminal in CoreTerminals)
            {
                terminal.AppendOutput($"[ERROR] Boot sequence failed: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// Launches QEMU with ARM and R5F cores using QemuController for proper architecture consistency
    /// </summary>
    private async Task LaunchQemuWithArmCores()
    {
        _logger.LogInfo("Step 1: Launching QEMU with ARM and R5F cores via QemuController");

        if (_configuration == null) return;

        // Get ALL configured ARM cores (A72 cores) with valid binary paths
        var configuredA72Cores = GetConfiguredCoresOfType(CoreType.A72_VM0, CoreType.A72_VM1);

        // Get ALL configured R5F cores with valid binary paths
        var configuredR5FCores = GetConfiguredCoresOfType(
            CoreType.MCU_R5F_0, CoreType.MCU_R5F_1,
            CoreType.Main_R5F_0, CoreType.Main_R5F_1, CoreType.Main_R5F_2, CoreType.Main_R5F_3, CoreType.Main_R5F_4, CoreType.Main_R5F_5);

        // Combine all cores for QEMU launch
        var allConfiguredCores = configuredA72Cores.Concat(configuredR5FCores).ToList();

        if (!allConfiguredCores.Any())
        {
            _logger.LogInfo("No ARM or R5F cores configured with valid binary paths, skipping QEMU launch");
            return;
        }

        _logger.LogInfo($"Launching QEMU with {allConfiguredCores.Count} cores ({configuredA72Cores.Count()} ARM, {configuredR5FCores.Count()} R5F)");

        // Use enhanced ProcessManager with QEMU support for multiple cores
        var coreConfigurations = CreateCoreConfigurations(allConfiguredCores);
        var success = await _processManager.StartQemuWithCoresAsync(_configuration.QemuBinaryPath, coreConfigurations);

        if (success)
        {
            _logger.LogInfo("QEMU launched successfully via enhanced ProcessManager");

            // Add success message to all configured core terminals
            foreach (var coreType in allConfiguredCores)
            {
                var terminal = CoreTerminals.FirstOrDefault(t => t.CoreType == coreType);
                var coreTypeDescription = IsArmCore(coreType) ? "ARM" : "R5F";
                terminal?.AppendOutput($"[SUCCESS] {coreTypeDescription} core {coreType.GetDisplayName()} loaded via QEMU (ProcessManager)");
            }
        }
        else
        {
            _logger.LogError("Failed to launch QEMU via enhanced ProcessManager");

            // Add error message to all configured core terminals
            foreach (var coreType in allConfiguredCores)
            {
                var terminal = CoreTerminals.FirstOrDefault(t => t.CoreType == coreType);
                terminal?.AppendOutput($"[ERROR] Failed to launch QEMU for {coreType.GetDisplayName()}");
            }
        }
    }

    /// <summary>
    /// Determines if a core type is an ARM core
    /// </summary>
    private static bool IsArmCore(CoreType coreType)
    {
        return coreType == CoreType.A72_VM0 || coreType == CoreType.A72_VM1;
    }

    /// <summary>
    /// Creates CoreConfiguration objects from core types for QEMU multi-core support
    /// </summary>
    private IEnumerable<CoreConfiguration> CreateCoreConfigurations(IEnumerable<CoreType> coreTypes)
    {
        var configurations = new List<CoreConfiguration>();

        foreach (var coreType in coreTypes)
        {
            var binaryPath = _configuration?.CoreBinaryPaths.GetPath(coreType);
            if (!string.IsNullOrWhiteSpace(binaryPath) && System.IO.File.Exists(binaryPath))
            {
                var config = new CoreConfiguration(coreType, binaryPath);
                configurations.Add(config);
                _logger.LogInfo($"Created core configuration: {coreType.GetDisplayName()} -> {binaryPath}");
            }
            else
            {
                _logger.LogWarning($"Skipping core {coreType.GetDisplayName()}: binary not found at {binaryPath}");
            }
        }

        return configurations;
    }

    /// <summary>
    /// Starts the IVSHMEM Server
    /// </summary>
    private Task StartIvshmemServer()
    {
        _logger.LogInfo("Step 2: Starting IVSHMEM Server");

        if (_configuration == null) return Task.CompletedTask;

        var ivshmemPath = _configuration.IvshmemServerBinaryPath;
        if (string.IsNullOrWhiteSpace(ivshmemPath))
        {
            _logger.LogInfo("IVSHMEM Server path not configured, skipping");
            return Task.CompletedTask;
        }

        if (!System.IO.File.Exists(ivshmemPath))
        {
            _logger.LogError($"IVSHMEM Server binary not found at: {ivshmemPath}");
            return Task.CompletedTask;
        }

        _logger.LogInfo($"Starting IVSHMEM Server: {ivshmemPath}");

        try
        {
            // Launch IVSHMEM Server as external process
            var processStartInfo = new System.Diagnostics.ProcessStartInfo
            {
                FileName = ivshmemPath,
                UseShellExecute = false,
                CreateNoWindow = true,
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };

            var process = System.Diagnostics.Process.Start(processStartInfo);
            if (process != null)
            {
                _logger.LogInfo($"IVSHMEM Server started successfully (PID: {process.Id})");
            }
            else
            {
                _logger.LogError("Failed to start IVSHMEM Server process");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error starting IVSHMEM Server: {ex.Message}");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Launches DSP cores only - R5F cores are now handled via QEMU
    /// </summary>
    private async Task LaunchDspCores()
    {
        _logger.LogInfo("Step 3: Launching DSP cores (R5F cores are handled via QEMU)");

        if (_configuration == null) return;

        // Get ALL configured DSP cores (C7x cores) with valid binary paths
        var configuredDspCores = GetConfiguredCoresOfType(
            CoreType.C7x_0, CoreType.C7x_1, CoreType.C7x_2, CoreType.C7x_3);

        if (!configuredDspCores.Any())
        {
            _logger.LogInfo("No DSP cores configured with valid binary paths, skipping DSP core launch");
            return;
        }

        _logger.LogInfo($"Launching {configuredDspCores.Count()} DSP cores");

        // Launch each DSP core as independent process
        foreach (var coreType in configuredDspCores)
        {
            var binaryPath = _configuration.CoreBinaryPaths.GetPath(coreType);
            if (!string.IsNullOrWhiteSpace(binaryPath) && System.IO.File.Exists(binaryPath))
            {
                _logger.LogInfo($"Launching DSP core: {coreType.GetDisplayName()} with binary: {binaryPath}");

                // Set binary path and start the core
                _processManager.SetBinaryPath(coreType, binaryPath);
                var success = await _processManager.StartCoreAsync(coreType);

                // Add output to core terminal
                var terminal = CoreTerminals.FirstOrDefault(t => t.CoreType == coreType);
                if (success)
                {
                    terminal?.AppendOutput($"[SUCCESS] DSP core {coreType.GetDisplayName()} launched");
                    _logger.LogInfo($"Successfully launched DSP core: {coreType.GetDisplayName()}");
                }
                else
                {
                    terminal?.AppendOutput($"[ERROR] Failed to launch DSP core {coreType.GetDisplayName()}");
                    _logger.LogError($"Failed to launch DSP core: {coreType.GetDisplayName()}");
                }
            }
            else
            {
                _logger.LogWarning($"Binary not found for DSP core: {coreType.GetDisplayName()} at path: {binaryPath}");
                var terminal = CoreTerminals.FirstOrDefault(t => t.CoreType == coreType);
                terminal?.AppendOutput($"[ERROR] Binary not found at: {binaryPath}");
            }
        }
    }

    /// <summary>
    /// Gets configured cores of specific types that have valid binary paths
    /// </summary>
    private IEnumerable<CoreType> GetConfiguredCoresOfType(params CoreType[] coreTypes)
    {
        if (_configuration == null) return Enumerable.Empty<CoreType>();

        var configuredCores = new List<CoreType>();

        foreach (var coreType in coreTypes)
        {
            var binaryPath = _configuration.CoreBinaryPaths.GetPath(coreType);
            if (!string.IsNullOrWhiteSpace(binaryPath) && System.IO.File.Exists(binaryPath))
            {
                configuredCores.Add(coreType);
            }
        }

        return configuredCores;
    }

    /// <summary>
    /// Stops emulation
    /// </summary>
    public async Task StopEmulationAsync()
    {
        try
        {
            _logger.LogInfo("Stopping emulation");

            // Stop QEMU process first
            await _processManager.StopQemuAsync();

            // Stop all individual core processes
            await _processManager.StopAllAsync();

            IsEmulationRunning = false;
            _logger.LogInfo("Emulation stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError("Error stopping emulation", ex);
        }
    }

    /// <summary>
    /// Sends message between selected cores with enhanced feedback
    /// </summary>
    private async Task SendMessageAsync()
    {
        try
        {
            if (!CanSendMessage)
            {
                _logger.LogWarning("Cannot send message - validation failed");
                return;
            }

            _logger.LogInfo($"Sending message: {Message}");

            // Get selected source and destination cores
            var sourceCores = SelectedSourceCores.ToList();
            var destCores = SelectedDestinationCores.ToList();

            _logger.LogInfo($"Message routing: {sourceCores.Count} source cores → {destCores.Count} destination cores");

            // Send IPC message to ALL selected source cores simultaneously
            var success = await _processManager.SendIpcMessageAsync(sourceCores, destCores, Message);

            // Provide clear feedback in terminal outputs
            if (success)
            {
                foreach (var sourceCore in sourceCores)
                {
                    var sourceTerminal = CoreTerminals.FirstOrDefault(t => t.CoreType == sourceCore);
                    if (sourceTerminal != null)
                    {
                        sourceTerminal.AppendOutput($"[MESSAGE SENT] To {destCores.Count} cores: \"{Message}\"");
                    }
                }

                foreach (var destCore in destCores)
                {
                    var destTerminal = CoreTerminals.FirstOrDefault(t => t.CoreType == destCore);
                    if (destTerminal != null)
                    {
                        destTerminal.AppendOutput($"[MESSAGE RECEIVED] From {sourceCores.Count} cores: \"{Message}\"");
                    }
                }

                _logger.LogInfo($"Message sent successfully to {sourceCores.Count} × {destCores.Count} = {sourceCores.Count * destCores.Count} core pairs");
            }
            else
            {
                _logger.LogWarning("Message sending failed - some cores may not be running");
            }

            // Clear message after sending
            Message = string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError("Error sending message", ex);
        }
    }

    /// <summary>
    /// Selects a communication channel
    /// </summary>
    private void SelectChannel(CommunicationChannelExpander channel)
    {
        _logger.LogInfo($"Selected communication channel: {channel.DisplayName}");
        SelectedCommunicationChannel = channel.DisplayName;
        ShowAllTerminals = false;
    }

    /// <summary>
    /// Handles output received from processes and routes to appropriate terminal
    /// </summary>
    private void OnOutputReceived(object? sender, TerminalLine line)
    {
        // Update UI on main thread
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            // Add to global terminal output
            TerminalOutput.Add(line);
            this.RaisePropertyChanged(nameof(TerminalOutputText));

            // Route output to specific core terminal
            var coreTerminal = CoreTerminals.FirstOrDefault(t => t.CoreType == line.SourceCore);
            if (coreTerminal != null)
            {
                // Format the output based on line type
                string formattedOutput = line.LineType switch
                {
                    TerminalLineType.StandardOutput => $"[STDOUT] {line.Text}",
                    TerminalLineType.ErrorOutput => $"[STDERR] {line.Text}",
                    TerminalLineType.SystemMessage => $"[SYSTEM] {line.Text}",
                    TerminalLineType.IpcMessage => $"[IPC] {line.Text}",
                    TerminalLineType.QemuSystemMessage => $"[QEMU] {line.Text}",
                    _ => line.Text
                };

                coreTerminal.AppendOutput(formattedOutput);
            }

            // Route output to communication channel expanders
            foreach (var channel in CommunicationChannels)
            {
                // Format the output based on line type
                string formattedOutput = line.LineType switch
                {
                    TerminalLineType.StandardOutput => $"[STDOUT] {line.Text}",
                    TerminalLineType.ErrorOutput => $"[STDERR] {line.Text}",
                    TerminalLineType.SystemMessage => $"[SYSTEM] {line.Text}",
                    TerminalLineType.IpcMessage => $"[IPC] {line.Text}",
                    TerminalLineType.QemuSystemMessage => $"[QEMU] {line.Text}",
                    _ => line.Text
                };

                // Route to source terminal if this line is from the source core
                if (channel.Source == line.SourceCore)
                {
                    channel.AppendSourceOutput(formattedOutput);
                }

                // Route to destination terminal if this line is from the destination core
                if (channel.Destination == line.SourceCore)
                {
                    channel.AppendDestinationOutput(formattedOutput);
                }
            }
        });
    }

    /// <summary>
    /// Handles process status changes
    /// </summary>
    private void OnProcessStatusChanged(object? sender, ProcessStatusChangedEventArgs e)
    {
        // Update UI on main thread
        Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
        {
            IsEmulationRunning = _processManager.IsAnyProcessRunning;
        });
    }

    /// <summary>
    /// Expands all expanders (communication channels and core terminals)
    /// </summary>
    private void ExpandAll()
    {
        _logger.LogInfo("Expanding all communication channels and terminal outputs");

        // Expand all communication channels
        foreach (var channel in CommunicationChannels)
        {
            channel.IsExpanded = true;
        }

        // Expand all core terminals
        foreach (var terminal in CoreTerminals)
        {
            terminal.IsExpanded = true;
        }
    }

    /// <summary>
    /// Collapses all expanders (communication channels and core terminals)
    /// </summary>
    private void CollapseAll()
    {
        _logger.LogInfo("Collapsing all communication channels and terminal outputs");

        // Collapse all communication channels
        foreach (var channel in CommunicationChannels)
        {
            channel.IsExpanded = false;
        }

        // Collapse all core terminals
        foreach (var terminal in CoreTerminals)
        {
            terminal.IsExpanded = false;
        }
    }

    /// <summary>
    /// Clears all outputs (communication channels and core terminals)
    /// </summary>
    private void ClearAll()
    {
        _logger.LogInfo("Clearing all communication channels and terminal outputs");

        // Clear all communication channels
        foreach (var channel in CommunicationChannels)
        {
            channel.ClearAllOutputs();
        }

        // Clear all core terminals
        foreach (var terminal in CoreTerminals)
        {
            terminal.ClearOutput();
        }
    }

    #endregion

    #region Public Methods

    /// <summary>
    /// Sets the configuration for emulation
    /// </summary>
    public void SetConfiguration(ApplicationConfiguration configuration)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        CurrentTestMode = configuration.SelectedTestMode;

        _logger.LogInfo($"Setting emulation configuration for test mode: {CurrentTestMode}");

        // Initialize core selections with only cores that have valid binary paths
        InitializeCoreSelectionsFromConfiguration();

        // Initialize core terminals
        InitializeCoreTerminals();

        // Update communication channels
        UpdateCommunicationChannels();

        // Update test mode indicators
        this.RaisePropertyChanged(nameof(IsIPCMode));
        this.RaisePropertyChanged(nameof(IsTestMode1));
        this.RaisePropertyChanged(nameof(IsTestMode2));
        this.RaisePropertyChanged(nameof(CanStartEmulation));
    }

    #endregion
}

/// <summary>
/// Represents a checkbox item for core selection following ReactiveUI pattern
/// </summary>
public class CheckboxItemViewModel : ReactiveObject
{
    private string _displayName = string.Empty;
    private bool _isChecked;
    private bool _isAllItem;
    private bool _isEnabled = true;
    private CoreType _coreType;

    public string DisplayName
    {
        get => _displayName;
        set => this.RaiseAndSetIfChanged(ref _displayName, value);
    }

    public bool IsChecked
    {
        get => _isChecked;
        set => this.RaiseAndSetIfChanged(ref _isChecked, value);
    }

    public bool IsAllItem
    {
        get => _isAllItem;
        set => this.RaiseAndSetIfChanged(ref _isAllItem, value);
    }

    public bool IsEnabled
    {
        get => _isEnabled;
        set => this.RaiseAndSetIfChanged(ref _isEnabled, value);
    }

    public CoreType CoreType
    {
        get => _coreType;
        set => this.RaiseAndSetIfChanged(ref _coreType, value);
    }
}



/// <summary>
/// Represents a communication channel between two cores
/// </summary>
public class CommunicationChannel
{
    public CommunicationChannel(CoreType source, CoreType destination)
    {
        Source = source;
        Destination = destination;
        DisplayName = $"{source.GetDisplayName()} <-> {destination.GetDisplayName()}";
    }

    /// <summary>
    /// Source core
    /// </summary>
    public CoreType Source { get; }

    /// <summary>
    /// Destination core
    /// </summary>
    public CoreType Destination { get; }

    /// <summary>
    /// Display name for the communication channel
    /// </summary>
    public string DisplayName { get; }
}

/// <summary>
/// Represents an expandable communication channel with dual terminal output
/// </summary>
public class CommunicationChannelExpander : ReactiveObject
{
    private bool _isExpanded;
    private string _sourceTerminalOutput = string.Empty;
    private string _destinationTerminalOutput = string.Empty;

    public CommunicationChannelExpander(CoreType source, CoreType destination)
    {
        Source = source;
        Destination = destination;
        DisplayName = $"{source.GetDisplayName()} → {destination.GetDisplayName()}";
        SourceCoreName = source.GetDisplayName();
        DestinationCoreName = destination.GetDisplayName();

        // Initialize commands
        ClearSourceCommand = ReactiveCommand.Create(ClearSourceOutput);
        ClearDestinationCommand = ReactiveCommand.Create(ClearDestinationOutput);
        ClearBothCommand = ReactiveCommand.Create(ClearBothOutputs);
    }

    /// <summary>
    /// Source core type
    /// </summary>
    public CoreType Source { get; }

    /// <summary>
    /// Destination core type
    /// </summary>
    public CoreType Destination { get; }

    /// <summary>
    /// Display name for the communication channel
    /// </summary>
    public string DisplayName { get; }

    /// <summary>
    /// Source core display name
    /// </summary>
    public string SourceCoreName { get; }

    /// <summary>
    /// Destination core display name
    /// </summary>
    public string DestinationCoreName { get; }

    /// <summary>
    /// Indicates if the expander is expanded
    /// </summary>
    public bool IsExpanded
    {
        get => _isExpanded;
        set => this.RaiseAndSetIfChanged(ref _isExpanded, value);
    }

    /// <summary>
    /// Terminal output for the source core
    /// </summary>
    public string SourceTerminalOutput
    {
        get => _sourceTerminalOutput;
        set => this.RaiseAndSetIfChanged(ref _sourceTerminalOutput, value);
    }

    /// <summary>
    /// Terminal output for the destination core
    /// </summary>
    public string DestinationTerminalOutput
    {
        get => _destinationTerminalOutput;
        set => this.RaiseAndSetIfChanged(ref _destinationTerminalOutput, value);
    }

    /// <summary>
    /// Command to clear source terminal output
    /// </summary>
    public ReactiveCommand<Unit, Unit> ClearSourceCommand { get; }

    /// <summary>
    /// Command to clear destination terminal output
    /// </summary>
    public ReactiveCommand<Unit, Unit> ClearDestinationCommand { get; }

    /// <summary>
    /// Command to clear both terminal outputs
    /// </summary>
    public ReactiveCommand<Unit, Unit> ClearBothCommand { get; }

    /// <summary>
    /// Appends text to the source terminal output
    /// </summary>
    public void AppendSourceOutput(string text)
    {
        SourceTerminalOutput += text + Environment.NewLine;
    }

    /// <summary>
    /// Appends text to the destination terminal output
    /// </summary>
    public void AppendDestinationOutput(string text)
    {
        DestinationTerminalOutput += text + Environment.NewLine;
    }

    /// <summary>
    /// Clears the source terminal output
    /// </summary>
    private void ClearSourceOutput()
    {
        SourceTerminalOutput = string.Empty;
    }

    /// <summary>
    /// Clears the destination terminal output
    /// </summary>
    private void ClearDestinationOutput()
    {
        DestinationTerminalOutput = string.Empty;
    }

    /// <summary>
    /// Clears both terminal outputs
    /// </summary>
    private void ClearBothOutputs()
    {
        SourceTerminalOutput = string.Empty;
        DestinationTerminalOutput = string.Empty;
    }

    /// <summary>
    /// Public method to clear both terminal outputs
    /// </summary>
    public void ClearAllOutputs()
    {
        ClearBothOutputs();
    }
}

/// <summary>
/// Represents an expandable terminal output for a specific core
/// </summary>
public class CoreTerminalExpander : ReactiveObject
{
    private bool _isExpanded;
    private string _terminalOutput = string.Empty;

    public CoreTerminalExpander(CoreType coreType)
    {
        CoreType = coreType;
        CoreName = coreType.GetDisplayName();
        ClearCommand = ReactiveCommand.Create(ClearOutput);
    }

    /// <summary>
    /// Core type
    /// </summary>
    public CoreType CoreType { get; }

    /// <summary>
    /// Display name for the core
    /// </summary>
    public string CoreName { get; }

    /// <summary>
    /// Command to clear this terminal's output
    /// </summary>
    public ReactiveCommand<Unit, Unit> ClearCommand { get; }

    /// <summary>
    /// Indicates if the expander is expanded
    /// </summary>
    public bool IsExpanded
    {
        get => _isExpanded;
        set => this.RaiseAndSetIfChanged(ref _isExpanded, value);
    }

    /// <summary>
    /// Terminal output for this core
    /// </summary>
    public string TerminalOutput
    {
        get => _terminalOutput;
        set => this.RaiseAndSetIfChanged(ref _terminalOutput, value);
    }

    /// <summary>
    /// Appends text to the terminal output
    /// </summary>
    public void AppendOutput(string text)
    {
        TerminalOutput += text + Environment.NewLine;
    }

    /// <summary>
    /// Clears the terminal output
    /// </summary>
    public void ClearOutput()
    {
        TerminalOutput = string.Empty;
    }
}

/// <summary>
/// Represents a row of terminals in the 3-column layout with dynamic height management
/// </summary>
public class TerminalRow : ReactiveObject, IDisposable
{
    private double _rowHeight = 55; // Default collapsed height
    private CommunicationChannelExpander? _terminal1;
    private CommunicationChannelExpander? _terminal2;
    private CommunicationChannelExpander? _terminal3;
    private readonly CompositeDisposable _subscriptions = new();

    /// <summary>
    /// First terminal in the row (column 0)
    /// </summary>
    public CommunicationChannelExpander? Terminal1
    {
        get => _terminal1;
        set
        {
            if (_terminal1 != value)
            {
                _terminal1 = value;
                this.RaisePropertyChanged();
                SetupTerminalSubscriptions();
            }
        }
    }

    /// <summary>
    /// Second terminal in the row (column 1)
    /// </summary>
    public CommunicationChannelExpander? Terminal2
    {
        get => _terminal2;
        set
        {
            if (_terminal2 != value)
            {
                _terminal2 = value;
                this.RaisePropertyChanged();
                SetupTerminalSubscriptions();
            }
        }
    }

    /// <summary>
    /// Third terminal in the row (column 2)
    /// </summary>
    public CommunicationChannelExpander? Terminal3
    {
        get => _terminal3;
        set
        {
            if (_terminal3 != value)
            {
                _terminal3 = value;
                this.RaisePropertyChanged();
                SetupTerminalSubscriptions();
            }
        }
    }

    /// <summary>
    /// Dynamic row height based on terminal states
    /// </summary>
    public double RowHeight
    {
        get => _rowHeight;
        private set => this.RaiseAndSetIfChanged(ref _rowHeight, value);
    }

    /// <summary>
    /// Sets up subscriptions for terminal expansion state changes
    /// </summary>
    private void SetupTerminalSubscriptions()
    {
        // Clear existing subscriptions
        _subscriptions.Clear();

        System.Diagnostics.Debug.WriteLine($"TerminalRow SetupTerminalSubscriptions: T1={Terminal1?.DisplayName}, T2={Terminal2?.DisplayName}, T3={Terminal3?.DisplayName}");

        // Subscribe to expansion state changes for non-null terminals
        if (Terminal1 != null)
        {
            _subscriptions.Add(Terminal1.WhenAnyValue(x => x.IsExpanded)
                .Subscribe(expanded => {
                    System.Diagnostics.Debug.WriteLine($"Terminal1 {Terminal1.DisplayName} IsExpanded changed to: {expanded}");
                    UpdateRowHeight();
                }));
        }

        if (Terminal2 != null)
        {
            _subscriptions.Add(Terminal2.WhenAnyValue(x => x.IsExpanded)
                .Subscribe(expanded => {
                    System.Diagnostics.Debug.WriteLine($"Terminal2 {Terminal2.DisplayName} IsExpanded changed to: {expanded}");
                    UpdateRowHeight();
                }));
        }

        if (Terminal3 != null)
        {
            _subscriptions.Add(Terminal3.WhenAnyValue(x => x.IsExpanded)
                .Subscribe(expanded => {
                    System.Diagnostics.Debug.WriteLine($"Terminal3 {Terminal3.DisplayName} IsExpanded changed to: {expanded}");
                    UpdateRowHeight();
                }));
        }

        // Update initial height
        UpdateRowHeight();
    }

    /// <summary>
    /// Updates row height based on terminal expansion states
    /// </summary>
    private void UpdateRowHeight()
    {
        // Check if any terminal is expanded
        bool anyExpanded = (Terminal1?.IsExpanded ?? false) ||
                          (Terminal2?.IsExpanded ?? false) ||
                          (Terminal3?.IsExpanded ?? false);

        // If any terminal is expanded, use full height; otherwise use collapsed height
        RowHeight = anyExpanded ? 550 : 55; // 550px for expanded, 55 for collapsed

        // Debug logging to track row height changes
        System.Diagnostics.Debug.WriteLine($"TerminalRow UpdateRowHeight: T1={Terminal1?.IsExpanded}, T2={Terminal2?.IsExpanded}, T3={Terminal3?.IsExpanded}, AnyExpanded={anyExpanded}, RowHeight={RowHeight}");
    }

    /// <summary>
    /// Dispose of subscriptions
    /// </summary>
    public void Dispose()
    {
        _subscriptions?.Dispose();
    }
}
