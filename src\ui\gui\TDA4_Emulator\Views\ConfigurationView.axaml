<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:vm="using:TDA4_Emulator.ViewModels"
             xmlns:models="using:TDA4_Emulator.Models"
             xmlns:converters="using:TDA4_Emulator.Converters"
             x:Class="TDA4_Emulator.Views.ConfigurationView"
             x:DataType="vm:ConfigurationViewModel">

    <UserControl.Resources>
        <converters:BoolToColorConverter x:Key="BoolToColorConverter"/>
        <converters:TestModeToDisplayNameConverter x:Key="TestModeToDisplayNameConverter"/>
        <converters:PathValidationToIconConverter x:Key="PathValidationToIconConverter"/>
        <converters:PathValidationToColorConverter x:Key="PathValidationToColorConverter"/>
    </UserControl.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Spacing="8" Margin="0,0,0,24">
            <TextBlock Text="Step 2: Configuration" 
                       FontSize="20" FontWeight="Bold" 
                       Foreground="#800020"/>
            <TextBlock Text="Binaries and settings" 
                       FontSize="14" Foreground="#666666"/>
        </StackPanel>

        <!-- Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="6*"/>
                <ColumnDefinition Width="4*"/>
            </Grid.ColumnDefinitions>

            <!-- Binary Paths Panel -->
            <ScrollViewer Grid.Column="0" Margin="0,0,32,0">
                <StackPanel Spacing="16">
                    <Label Content="Binary paths" 
                           Classes="section" FontSize="16"/>

                    <!-- ARM A72 Group -->
                    <StackPanel Spacing="8">
                        <Label Content="ARM A72" FontWeight="Bold" Foreground="#800020"/>
                        <ItemsControl ItemsSource="{Binding A72Cores}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,4" IsVisible="{Binding IsVisible}">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="100"/>
                                            <ColumnDefinition Width="500"/>
                                            <ColumnDefinition Width="30"/>
                                            <ColumnDefinition Width="100"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <Label Grid.Column="0" Content="{Binding DisplayName}" 
                                               VerticalAlignment="Center"/>
                                        <TextBox Grid.Column="1" Text="{Binding BinaryPath}"
                                                 Classes="path" Watermark="Select binary executable..."/>
                                        <TextBlock Grid.Column="2" VerticalAlignment="Center" Margin="4,0" FontWeight="Bold" FontSize="14">
                                            <TextBlock.Text>
                                                <MultiBinding Converter="{StaticResource PathValidationToIconConverter}">
                                                    <Binding Path="BinaryPath"/>
                                                    <Binding Path="IsPathValid"/>
                                                </MultiBinding>
                                            </TextBlock.Text>
                                            <TextBlock.Foreground>
                                                <MultiBinding Converter="{StaticResource PathValidationToColorConverter}">
                                                    <Binding Path="BinaryPath"/>
                                                    <Binding Path="IsPathValid"/>
                                                </MultiBinding>
                                            </TextBlock.Foreground>
                                        </TextBlock>
                                        <Button Grid.Column="3" Content="Browse" Classes="secondary"
                                                Command="{Binding $parent[UserControl].DataContext.BrowseBinaryCommand}"
                                                CommandParameter="{Binding CoreType}"
                                                Margin="8,0,24,0"/>
                                    </Grid>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                        <Separator Background="#CCCCCC" Height="1" Margin="0,8"/>
                    </StackPanel>

                    <!-- ARM MCU R5F Group -->
                    <StackPanel Spacing="8">
                        <Label Content="ARM MCU R5F" FontWeight="Bold" Foreground="#800020"/>
                        <ItemsControl ItemsSource="{Binding McuR5FCores}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,4" IsVisible="{Binding IsVisible}">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="100"/>
                                            <ColumnDefinition Width="500"/>
                                            <ColumnDefinition Width="30"/>
                                            <ColumnDefinition Width="100"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <Label Grid.Column="0" Content="{Binding DisplayName}" 
                                               VerticalAlignment="Center"/>
                                        <TextBox Grid.Column="1" Text="{Binding BinaryPath}"
                                                 Classes="path" Watermark="Select binary executable..."/>
                                        <TextBlock Grid.Column="2" VerticalAlignment="Center" Margin="4,0" FontWeight="Bold" FontSize="14">
                                            <TextBlock.Text>
                                                <MultiBinding Converter="{StaticResource PathValidationToIconConverter}">
                                                    <Binding Path="BinaryPath"/>
                                                    <Binding Path="IsPathValid"/>
                                                </MultiBinding>
                                            </TextBlock.Text>
                                            <TextBlock.Foreground>
                                                <MultiBinding Converter="{StaticResource PathValidationToColorConverter}">
                                                    <Binding Path="BinaryPath"/>
                                                    <Binding Path="IsPathValid"/>
                                                </MultiBinding>
                                            </TextBlock.Foreground>
                                        </TextBlock>
                                        <Button Grid.Column="3" Content="Browse" Classes="secondary"
                                                Command="{Binding $parent[UserControl].DataContext.BrowseBinaryCommand}"
                                                CommandParameter="{Binding CoreType}"
                                                Margin="8,0,24,0"/>
                                    </Grid>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                        <Separator Background="#CCCCCC" Height="1" Margin="0,8"/>
                    </StackPanel>

                    <!-- ARM Main R5F Group -->
                    <StackPanel Spacing="8">
                        <Label Content="ARM Main R5F" FontWeight="Bold" Foreground="#800020"/>
                        <ItemsControl ItemsSource="{Binding MainR5FCores}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,4" IsVisible="{Binding IsVisible}">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="100"/>
                                            <ColumnDefinition Width="500"/>
                                            <ColumnDefinition Width="30"/>
                                            <ColumnDefinition Width="100"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <Label Grid.Column="0" Content="{Binding DisplayName}" 
                                               VerticalAlignment="Center"/>
                                        <TextBox Grid.Column="1" Text="{Binding BinaryPath}"
                                                 Classes="path" Watermark="Select binary executable..."/>
                                        <TextBlock Grid.Column="2" VerticalAlignment="Center" Margin="4,0" FontWeight="Bold" FontSize="14">
                                            <TextBlock.Text>
                                                <MultiBinding Converter="{StaticResource PathValidationToIconConverter}">
                                                    <Binding Path="BinaryPath"/>
                                                    <Binding Path="IsPathValid"/>
                                                </MultiBinding>
                                            </TextBlock.Text>
                                            <TextBlock.Foreground>
                                                <MultiBinding Converter="{StaticResource PathValidationToColorConverter}">
                                                    <Binding Path="BinaryPath"/>
                                                    <Binding Path="IsPathValid"/>
                                                </MultiBinding>
                                            </TextBlock.Foreground>
                                        </TextBlock>
                                        <Button Grid.Column="3" Content="Browse" Classes="secondary"
                                                Command="{Binding $parent[UserControl].DataContext.BrowseBinaryCommand}"
                                                CommandParameter="{Binding CoreType}"
                                                Margin="8,0,24,0"/>
                                    </Grid>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                        <Separator Background="#CCCCCC" Height="1" Margin="0,8"/>
                    </StackPanel>

                    <!-- DSP C7x Group -->
                    <StackPanel Spacing="8">
                        <Label Content="DSP C7x" FontWeight="Bold" Foreground="#800020"/>
                        <ItemsControl ItemsSource="{Binding C7xCores}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,4" IsVisible="{Binding IsVisible}">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="100"/>
                                            <ColumnDefinition Width="500"/>
                                            <ColumnDefinition Width="30"/>
                                            <ColumnDefinition Width="100"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <Label Grid.Column="0" Content="{Binding DisplayName}" 
                                               VerticalAlignment="Center"/>
                                        <TextBox Grid.Column="1" Text="{Binding BinaryPath}"
                                                 Classes="path" Watermark="Select binary executable..."/>
                                        <TextBlock Grid.Column="2" VerticalAlignment="Center" Margin="4,0" FontWeight="Bold" FontSize="14">
                                            <TextBlock.Text>
                                                <MultiBinding Converter="{StaticResource PathValidationToIconConverter}">
                                                    <Binding Path="BinaryPath"/>
                                                    <Binding Path="IsPathValid"/>
                                                </MultiBinding>
                                            </TextBlock.Text>
                                            <TextBlock.Foreground>
                                                <MultiBinding Converter="{StaticResource PathValidationToColorConverter}">
                                                    <Binding Path="BinaryPath"/>
                                                    <Binding Path="IsPathValid"/>
                                                </MultiBinding>
                                            </TextBlock.Foreground>
                                        </TextBlock>
                                        <Button Grid.Column="3" Content="Browse" Classes="secondary"
                                                Command="{Binding $parent[UserControl].DataContext.BrowseBinaryCommand}"
                                                CommandParameter="{Binding CoreType}"
                                                Margin="8,0,0,0"/>
                                    </Grid>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>

            <!-- Settings Panel -->
            <StackPanel Grid.Column="1" Spacing="16">
                <Label Content="Settings" Classes="section" FontSize="16"/>
                
                <Border BorderBrush="#CCCCCC" BorderThickness="1" 
                        CornerRadius="4" Padding="16" Background="White">
                    <StackPanel Spacing="16">
                        
                        <!-- QEMU Binary Path -->
                        <StackPanel Spacing="4">
                            <Label Content="QEMU Binary Path"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox Grid.Column="0" Text="{Binding QemuBinaryPath}"
                                         Classes="path" Watermark="Select QEMU binary..."/>
                                <!-- Validation Icon -->
                                <TextBlock Grid.Column="1" Text="{Binding QemuPathValidationIcon}"
                                           Foreground="{Binding QemuPathValidationColor}"
                                           FontSize="16" VerticalAlignment="Center"
                                           Margin="8,0,4,0"/>
                                <Button Grid.Column="2" Content="Browse" Classes="secondary"
                                        Command="{Binding BrowseQemuBinaryCommand}"/>
                            </Grid>
                            <TextBlock Text="Path to QEMU emulator binary for ARM core emulation (A72 and R5F)"
                                    FontSize="11" Foreground="#666666" Margin="0,0,0,4"/>
                            <TextBlock Text="{Binding QemuPathError}" Foreground="Red"
                                       FontSize="11" IsVisible="{Binding QemuPathError, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
                        </StackPanel>

                        <!-- IVSHMEM Server Binary Path -->
                        <StackPanel Spacing="4">
                            <Label Content="IVSHMEM Server Binary Path"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBox Grid.Column="0" Text="{Binding IvshmemServerBinaryPath}"
                                         Classes="path" Watermark="Select IVSHMEM Server binary..."/>
                                <!-- Validation Icon -->
                                <TextBlock Grid.Column="1" Text="{Binding IvshmemPathValidationIcon}"
                                           Foreground="{Binding IvshmemPathValidationColor}"
                                           FontSize="16" VerticalAlignment="Center"
                                           Margin="8,0,4,0"/>
                                <Button Grid.Column="2" Content="Browse" Classes="secondary"
                                        Command="{Binding BrowseIvshmemServerBinaryCommand}"/>
                            </Grid>
                            <TextBlock Text="Path to the ivshmem server for communication between the C7x DSP and QEMU (R5F and A72)"
                                       FontSize="11" Foreground="#666666" Margin="0,0,0,4"/>
                            <TextBlock Text="{Binding IvshmemPathError}" Foreground="Red"
                                       FontSize="11" IsVisible="{Binding IvshmemPathError, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
                        </StackPanel>

                        <!-- Test Mode Selection -->
                        <StackPanel Spacing="4">
                            <Label Content="Test Mode"/>
                            <ComboBox ItemsSource="{Binding AvailableTestModes}"
                                      SelectedItem="{Binding SelectedTestMode}"
                                      HorizontalAlignment="Stretch"
                                      MinHeight="32">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate DataType="models:TestMode">
                                        <TextBlock Text="{Binding Converter={StaticResource TestModeToDisplayNameConverter}}"/>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                            <TextBlock Text="{Binding SelectedTestModeDescription}"
                                       TextWrapping="Wrap"
                                       FontSize="11"
                                       Foreground="#666666"
                                       Margin="0,4,0,0"/>
                        </StackPanel>

                        <!-- R5F Mode Selection -->
                        <StackPanel Spacing="4">
                            <Label Content="R5F Mode"/>
                            <ComboBox ItemsSource="{Binding AvailableR5FModes}"
                                      SelectedItem="{Binding SelectedR5FMode}"
                                      HorizontalAlignment="Stretch"
                                      MinHeight="32">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate DataType="models:R5FMode">
                                        <TextBlock Text="{Binding}"/>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                            <TextBlock Text="{Binding SelectedR5FModeDescription}"
                                       TextWrapping="Wrap"
                                       FontSize="11"
                                       Foreground="#666666"
                                       Margin="0,4,0,0"/>
                        </StackPanel>

                        <!-- Configuration Buttons -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Button Grid.Column="0" Content="Reset configuration" Classes="secondary"
                                    Command="{Binding ResetConfigurationCommand}"
                                    Margin="0,0,4,0"/>
                            <Button Grid.Column="1" Content="Load configuration" Classes="secondary"
                                    Command="{Binding LoadConfigurationCommand}"
                                    Margin="4,0,0,0"/>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </Grid>

        <!-- Navigation -->
        <Grid Grid.Row="2" Margin="0,24,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- Back Button -->
            <Button Grid.Column="0" Content="Back" Classes="secondary"
                    Command="{Binding BackCommand}"
                    MinWidth="100" Padding="16,8"/>

            <!-- Next Button -->
            <Button Grid.Column="2" Content="Next" Classes="primary"
                    Command="{Binding NextCommand}"
                    IsEnabled="{Binding CanNavigateNext}"
                    MinWidth="100" Padding="16,8"/>
        </Grid>
    </Grid>

</UserControl>
