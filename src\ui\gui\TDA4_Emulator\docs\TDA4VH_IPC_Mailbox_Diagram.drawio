<mxfile host="Electron" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/27.0.2 Chrome/134.0.6998.205 Electron/35.3.0 Safari/537.36" version="27.0.2" pages="4">
  <diagram name="main" id="XxmGeSufUnfhE8LcMwkk">
    <mxGraphModel dx="2512" dy="1072" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="1654" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <UserObject label="" link="./ivshmem-client -S /tmp/ivshmem.sock" id="h9OmZHuHNuUbkvZkJMz0-62">
          <mxCell style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
            <mxGeometry x="1626" y="306" width="1498" height="725" as="geometry" />
          </mxCell>
        </UserObject>
        <mxCell id="bFK34A6A18MXLZeqNGrH-1" value="Host (x86-64)" style="swimlane;whiteSpace=wrap;html=1;startSize=40;fontFamily=Helvetica;" parent="1" vertex="1">
          <mxGeometry x="40" y="40" width="1411" height="671" as="geometry" />
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-2" value="TDA4 SoC Machine (QEMU process)" style="swimlane;whiteSpace=wrap;html=1;startSize=40;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-1" vertex="1">
          <mxGeometry x="20" y="60" width="727" height="442" as="geometry" />
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=1;entryDx=0;entryDy=0;fontFamily=Helvetica;exitX=0.25;exitY=0;exitDx=0;exitDy=0;" parent="bFK34A6A18MXLZeqNGrH-2" source="G7bFaSlycRX9vyD2iu5w-1" target="bFK34A6A18MXLZeqNGrH-12" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="366" y="302" as="sourcePoint" />
            <mxPoint x="370" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-4" value="read/write&lt;div&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(3.5)&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;fontSize=16;" parent="bFK34A6A18MXLZeqNGrH-3" vertex="1" connectable="0">
          <mxGeometry x="-0.0143" y="1" relative="1" as="geometry">
            <mxPoint x="-5" y="24" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rDPpt13hlx4OZT86Hix_-9" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="bFK34A6A18MXLZeqNGrH-2" source="G7bFaSlycRX9vyD2iu5w-1" target="rDPpt13hlx4OZT86Hix_-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rDPpt13hlx4OZT86Hix_-10" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;/font&gt;&lt;div&gt;&lt;span style=&quot;font-size: 16px; color: rgb(0, 255, 0);&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.4)&lt;/font&gt;&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 255, 0); font-size: 16px;&quot;&gt;(3.4)&lt;/span&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="rDPpt13hlx4OZT86Hix_-9" vertex="1" connectable="0">
          <mxGeometry x="0.1157" relative="1" as="geometry">
            <mxPoint x="5" y="3" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-5" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;ivshmem-client&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;(QEMU device)&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-2" vertex="1">
          <mxGeometry x="325" y="333" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.75;entryDx=0;entryDy=0;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-2" target="bFK34A6A18MXLZeqNGrH-15" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="340" y="157" as="sourcePoint" />
            <mxPoint x="260" y="157" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-7" value="notify (IRQ)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-6" vertex="1" connectable="0">
          <mxGeometry x="0.0298" relative="1" as="geometry">
            <mxPoint x="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;entryX=0;entryY=0.75;entryDx=0;entryDy=0;exitX=1;exitY=0.75;exitDx=0;exitDy=0;exitPerimeter=0;" parent="bFK34A6A18MXLZeqNGrH-2" source="bFK34A6A18MXLZeqNGrH-12" target="bFK34A6A18MXLZeqNGrH-18" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="460" y="157" as="sourcePoint" />
            <mxPoint x="540" y="157" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-9" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;notify (IRQ)&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px; color: rgb(0, 255, 0);&quot;&gt;(4.1)&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-8" vertex="1" connectable="0">
          <mxGeometry x="-0.3393" y="1" relative="1" as="geometry">
            <mxPoint x="15" y="14" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0.75;exitY=1;exitDx=0;exitDy=0;" parent="bFK34A6A18MXLZeqNGrH-2" source="bFK34A6A18MXLZeqNGrH-12" target="G7bFaSlycRX9vyD2iu5w-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="429" y="140" as="sourcePoint" />
            <mxPoint x="426" y="298" as="targetPoint" />
            <Array as="points">
              <mxPoint x="426" y="262" />
              <mxPoint x="426" y="262" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-11" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;notify (IRQ)&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px; color: rgb(255, 153, 51);&quot;&gt;(1.3)&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-10" vertex="1" connectable="0">
          <mxGeometry x="-0.119" relative="1" as="geometry">
            <mxPoint x="1" y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-12" value="Mailbox model&lt;div&gt;(QEMU device)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-2" vertex="1">
          <mxGeometry x="336" y="97" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.25;exitDx=0;exitDy=0;fontFamily=Helvetica;entryX=0;entryY=0.25;entryDx=0;entryDy=0;" parent="bFK34A6A18MXLZeqNGrH-2" source="bFK34A6A18MXLZeqNGrH-15" target="bFK34A6A18MXLZeqNGrH-12" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="280" y="117" as="sourcePoint" />
            <mxPoint x="340" y="117" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-14" value="read/write" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-13" vertex="1" connectable="0">
          <mxGeometry x="-0.6488" y="-1" relative="1" as="geometry">
            <mxPoint x="22" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-15" value="R5F&lt;div&gt;(QEMU device)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-2" vertex="1">
          <mxGeometry x="164" y="97" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0;exitY=0.25;exitDx=0;exitDy=0;entryX=0.98;entryY=0.26;entryDx=0;entryDy=0;entryPerimeter=0;" parent="bFK34A6A18MXLZeqNGrH-2" source="bFK34A6A18MXLZeqNGrH-18" target="bFK34A6A18MXLZeqNGrH-12" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="540" y="118" as="sourcePoint" />
            <mxPoint x="460" y="118" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-17" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.2)&lt;/font&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(4.2)&lt;/font&gt;&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-16" vertex="1" connectable="0">
          <mxGeometry x="0.256" y="-1" relative="1" as="geometry">
            <mxPoint x="15" y="-11" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="a1OmAsUUNiDIbFJyHpuR-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;curved=1;exitX=0.25;exitY=0;exitDx=0;exitDy=0;" parent="bFK34A6A18MXLZeqNGrH-2" source="bFK34A6A18MXLZeqNGrH-18" target="rDPpt13hlx4OZT86Hix_-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="590" y="97" />
              <mxPoint x="590" y="60" />
              <mxPoint x="95" y="60" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="a1OmAsUUNiDIbFJyHpuR-10" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.1)&lt;/font&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(5)&lt;/font&gt; read/write&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="a1OmAsUUNiDIbFJyHpuR-9" vertex="1" connectable="0">
          <mxGeometry x="0.1318" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-18" value="A72&lt;div&gt;(QEMU device)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-2" vertex="1">
          <mxGeometry x="595" y="97" width="100" height="80" as="geometry" />
        </mxCell>
        <mxCell id="a1OmAsUUNiDIbFJyHpuR-7" value="" style="group" parent="bFK34A6A18MXLZeqNGrH-2" vertex="1" connectable="0">
          <mxGeometry x="7" y="100" width="100" height="230" as="geometry" />
        </mxCell>
        <mxCell id="rDPpt13hlx4OZT86Hix_-3" value="Memory" style="rounded=0;whiteSpace=wrap;html=1;align=center;labelPosition=center;verticalLabelPosition=top;verticalAlign=bottom;fontFamily=Helvetica;" parent="a1OmAsUUNiDIbFJyHpuR-7" vertex="1">
          <mxGeometry width="100" height="230" as="geometry" />
        </mxCell>
        <mxCell id="rDPpt13hlx4OZT86Hix_-5" value="" style="group;fontFamily=Helvetica;" parent="a1OmAsUUNiDIbFJyHpuR-7" vertex="1" connectable="0">
          <mxGeometry y="80" width="100" height="120" as="geometry" />
        </mxCell>
        <mxCell id="rDPpt13hlx4OZT86Hix_-6" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Share memory&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;(QEMU)&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="rDPpt13hlx4OZT86Hix_-5" vertex="1">
          <mxGeometry width="100" height="120" as="geometry" />
        </mxCell>
        <mxCell id="rDPpt13hlx4OZT86Hix_-7" value="VRING-0" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="rDPpt13hlx4OZT86Hix_-5" vertex="1">
          <mxGeometry width="100" height="26.666666666666668" as="geometry" />
        </mxCell>
        <mxCell id="rDPpt13hlx4OZT86Hix_-8" value="VRING-n" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="rDPpt13hlx4OZT86Hix_-5" vertex="1">
          <mxGeometry y="93.33333333333334" width="100" height="26.666666666666668" as="geometry" />
        </mxCell>
        <mxCell id="a1OmAsUUNiDIbFJyHpuR-8" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1.01;entryY=0.302;entryDx=0;entryDy=0;entryPerimeter=0;" parent="bFK34A6A18MXLZeqNGrH-2" source="bFK34A6A18MXLZeqNGrH-15" target="rDPpt13hlx4OZT86Hix_-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a1OmAsUUNiDIbFJyHpuR-11" value="read/write" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="a1OmAsUUNiDIbFJyHpuR-8" vertex="1" connectable="0">
          <mxGeometry x="0.0347" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="G7bFaSlycRX9vyD2iu5w-1" value="&lt;div&gt;PCI-Bar&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-2" vertex="1">
          <mxGeometry x="325" y="291" width="160" height="42" as="geometry" />
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-19" value="C71x (Non QEMU process)" style="swimlane;whiteSpace=wrap;html=1;startSize=40;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-1" vertex="1">
          <mxGeometry x="1040" y="60" width="306" height="452" as="geometry" />
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="bFK34A6A18MXLZeqNGrH-19" source="bFK34A6A18MXLZeqNGrH-22" target="a1OmAsUUNiDIbFJyHpuR-4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="201" y="276" as="sourcePoint" />
            <mxPoint x="201" y="196" as="targetPoint" />
            <Array as="points">
              <mxPoint x="275" y="368" />
              <mxPoint x="275" y="160" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-21" value="&lt;div&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(2.1)&lt;/font&gt;&lt;/div&gt;notify" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;fontSize=16;" parent="bFK34A6A18MXLZeqNGrH-20" vertex="1" connectable="0">
          <mxGeometry x="-0.0321" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-22" value="ivshmem-client" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-19" vertex="1">
          <mxGeometry x="50" y="333" width="200" height="70" as="geometry" />
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;entryX=0.073;entryY=0.033;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.143;exitY=0.975;exitDx=0;exitDy=0;exitPerimeter=0;" parent="bFK34A6A18MXLZeqNGrH-19" target="bFK34A6A18MXLZeqNGrH-22" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="63.01999999999998" y="178.9999999999999" as="sourcePoint" />
            <mxPoint x="63.07999999999993" y="259.01999999999987" as="targetPoint" />
            <Array as="points">
              <mxPoint x="64" y="179" />
              <mxPoint x="64" y="256" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-24" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(153, 204, 255);&quot;&gt;&lt;b style=&quot;&quot;&gt;(2)&lt;/b&gt;&lt;/font&gt;&amp;nbsp;Initialize&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-23" vertex="1" connectable="0">
          <mxGeometry x="0.1" relative="1" as="geometry">
            <mxPoint y="6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-19" target="bFK34A6A18MXLZeqNGrH-22" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="201" y="180" as="sourcePoint" />
            <mxPoint x="202.02040816326553" y="260" as="targetPoint" />
            <Array as="points">
              <mxPoint x="201" y="220" />
              <mxPoint x="202" y="220" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-26" value="&lt;div&gt;&lt;font style=&quot;font-size: 16px; color: rgb(255, 153, 51);&quot;&gt;(2.2)&lt;/font&gt;&lt;font style=&quot;font-size: 16px; color: rgb(0, 255, 0);&quot;&gt;(2.1)&lt;/font&gt;&lt;/div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;vring&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-25" vertex="1" connectable="0">
          <mxGeometry x="-0.1393" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0.363;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.393;entryY=-0.005;entryDx=0;entryDy=0;entryPerimeter=0;" parent="bFK34A6A18MXLZeqNGrH-19" target="bFK34A6A18MXLZeqNGrH-22" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="121.59999999999991" y="180" as="sourcePoint" />
            <mxPoint x="129" y="260" as="targetPoint" />
            <Array as="points">
              <mxPoint x="129" y="180" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-29" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(3.1)&lt;/font&gt; kick&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-28" vertex="1" connectable="0">
          <mxGeometry x="-0.1583" y="1" relative="1" as="geometry">
            <mxPoint y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="a1OmAsUUNiDIbFJyHpuR-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="bFK34A6A18MXLZeqNGrH-19" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="64" y="100" as="sourcePoint" />
            <mxPoint x="64" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="a1OmAsUUNiDIbFJyHpuR-6" value="&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(1)&lt;/font&gt; send msg" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=16;" parent="a1OmAsUUNiDIbFJyHpuR-5" vertex="1" connectable="0">
          <mxGeometry x="-0.08" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="a1OmAsUUNiDIbFJyHpuR-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="bFK34A6A18MXLZeqNGrH-19" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="196" y="100" as="sourcePoint" />
            <mxPoint x="196" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="a1OmAsUUNiDIbFJyHpuR-25" value="&lt;font style=&quot;font-size: 16px; color: rgb(255, 153, 51);&quot;&gt;(4)&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;recv msg&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="a1OmAsUUNiDIbFJyHpuR-24" vertex="1" connectable="0">
          <mxGeometry x="-0.3047" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-27" value="Application" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-19" vertex="1">
          <mxGeometry x="40" y="60" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="a1OmAsUUNiDIbFJyHpuR-4" value="IPC Driver" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-19" vertex="1">
          <mxGeometry x="40" y="140" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="rDPpt13hlx4OZT86Hix_-23" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="bFK34A6A18MXLZeqNGrH-1" source="bFK34A6A18MXLZeqNGrH-30" target="bFK34A6A18MXLZeqNGrH-44" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rDPpt13hlx4OZT86Hix_-24" value="&lt;font style=&quot;font-size: 16px; color: rgb(153, 204, 255);&quot;&gt;&lt;b&gt;(1)&lt;/b&gt;&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;initialize&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="rDPpt13hlx4OZT86Hix_-23" vertex="1" connectable="0">
          <mxGeometry x="-0.1061" y="-1" relative="1" as="geometry">
            <mxPoint x="-1" y="-34" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-30" value="ivshmem-server process" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-1" vertex="1">
          <mxGeometry x="654" y="544" width="240" height="80" as="geometry" />
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-31" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-1" source="bFK34A6A18MXLZeqNGrH-22" target="bFK34A6A18MXLZeqNGrH-30" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1180" y="450" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-32" value="&lt;font style=&quot;font-size: 16px; color: rgb(153, 204, 255);&quot;&gt;&lt;b&gt;(2)&lt;/b&gt;&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;connect&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-31" vertex="1" connectable="0">
          <mxGeometry x="0.4148" relative="1" as="geometry">
            <mxPoint x="-4" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-1" source="bFK34A6A18MXLZeqNGrH-5" target="bFK34A6A18MXLZeqNGrH-30" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="440" y="584" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-34" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(153, 204, 255);&quot;&gt;&lt;b style=&quot;&quot;&gt;(2)&lt;/b&gt;&lt;/font&gt; connect&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-33" vertex="1" connectable="0">
          <mxGeometry x="0.5029" y="1" relative="1" as="geometry">
            <mxPoint x="-20" y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;startArrow=classic;startFill=1;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-1" source="bFK34A6A18MXLZeqNGrH-5" target="bFK34A6A18MXLZeqNGrH-22" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-36" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;span style=&quot;color: rgb(0, 255, 0);&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.6)&lt;/font&gt;&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 255, 0);&quot;&gt;(3.2)&amp;nbsp;&lt;/span&gt;Notify &quot;VRING ID&quot;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-35" vertex="1" connectable="0">
          <mxGeometry x="-0.1593" y="1" relative="1" as="geometry">
            <mxPoint x="178" y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-37" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1.011;entryY=0.679;entryDx=0;entryDy=0;entryPerimeter=0;fontFamily=Helvetica;exitX=0;exitY=0;exitDx=0;exitDy=0;" parent="bFK34A6A18MXLZeqNGrH-1" source="bFK34A6A18MXLZeqNGrH-22" target="bFK34A6A18MXLZeqNGrH-42" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="a1OmAsUUNiDIbFJyHpuR-1" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(3)&lt;/font&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(2.2)&lt;/font&gt;&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="bFK34A6A18MXLZeqNGrH-37" vertex="1" connectable="0">
          <mxGeometry x="0.0091" y="-1" relative="1" as="geometry">
            <mxPoint x="-19" y="-10" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-39" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="bFK34A6A18MXLZeqNGrH-1" source="G7bFaSlycRX9vyD2iu5w-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="799" y="212" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-40" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;br&gt;&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;span style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); color: rgb(0, 255, 0);&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.5)&lt;/font&gt;&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 255, 0); background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));&quot;&gt;(3.3)&lt;/span&gt;&amp;nbsp;&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-39" vertex="1" connectable="0">
          <mxGeometry x="0.0258" y="2" relative="1" as="geometry">
            <mxPoint x="-32" y="17" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-41" value="" style="group;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-1" vertex="1" connectable="0">
          <mxGeometry x="800" y="60" width="120" height="180" as="geometry" />
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-42" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Share memory&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;(Host)&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-41" vertex="1">
          <mxGeometry width="120" height="180" as="geometry" />
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-43" value="VRING-0" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-41" vertex="1">
          <mxGeometry width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-44" value="VRING-n" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="bFK34A6A18MXLZeqNGrH-41" vertex="1">
          <mxGeometry y="140" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="a1OmAsUUNiDIbFJyHpuR-16" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="bFK34A6A18MXLZeqNGrH-1" target="bFK34A6A18MXLZeqNGrH-30" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="737" y="756" as="sourcePoint" />
            <mxPoint x="870" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="a1OmAsUUNiDIbFJyHpuR-17" value="&lt;font style=&quot;font-size: 16px; color: rgb(153, 204, 255);&quot;&gt;&lt;b&gt;(1)&lt;/b&gt;&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;start&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="a1OmAsUUNiDIbFJyHpuR-16" vertex="1" connectable="0">
          <mxGeometry x="-0.1061" y="-1" relative="1" as="geometry">
            <mxPoint x="-6" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bFK34A6A18MXLZeqNGrH-45" value="&lt;div&gt;&lt;b&gt;&lt;font style=&quot;font-size: 15px;&quot;&gt;C7x&amp;nbsp;→ A72/R5F&lt;/font&gt;&lt;/b&gt;&lt;/div&gt;&lt;ol style=&quot;line-height: 200%;&quot;&gt;&lt;li&gt;A application sends a message to a given destination (CPU, endpoint).&lt;/li&gt;&lt;li&gt;The message is first copied from the application to VRING used between the two CPUs.&lt;/li&gt;&lt;li&gt;After this the IPC driver posts the VRING ID in the HW mailbox.&lt;/li&gt;&lt;li&gt;This triggers a interrupt on the destination CPU. In the ISR of destination CPU, it extracts the VRING ID and then based on the VRING ID, checks for any messages in that VRING&lt;/li&gt;&lt;li&gt;If a message is received, it extracts the message from the VRING and puts it in the destination RPMSG endpoint queue. It then triggers the application blocked on this RPMSG endpoint&lt;/li&gt;&lt;/ol&gt;" style="text;html=1;align=left;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=#82b366;fillColor=#d5e8d4;fontFamily=Helvetica;textDirection=ltr;" parent="1" vertex="1">
          <mxGeometry x="40" y="1105" width="1037" height="174" as="geometry" />
        </mxCell>
        <mxCell id="rDPpt13hlx4OZT86Hix_-11" value="&lt;div&gt;&lt;b&gt;&lt;font style=&quot;font-size: 15px;&quot;&gt;Initialization&lt;/font&gt;&lt;/b&gt;&lt;/div&gt;&lt;ol style=&quot;line-height: 200%;&quot;&gt;&lt;li&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Start ivshmem-server process. Server initializes a shared memory on Host for exchanging VRING data between processes.&lt;/span&gt;&lt;/li&gt;&lt;li&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Start QEMU and non-QEMU process. ivshmem-client connect to server. The server responds to the client with the information: clientID, other client fd, shared memory fd&lt;/span&gt;&lt;/li&gt;&lt;/ol&gt;" style="text;html=1;align=left;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=#6c8ebf;fillColor=#dae8fc;fontFamily=Helvetica;" parent="1" vertex="1">
          <mxGeometry x="40" y="985" width="960" height="100" as="geometry" />
        </mxCell>
        <mxCell id="rDPpt13hlx4OZT86Hix_-13" value="&lt;div&gt;&lt;b style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;font style=&quot;font-size: 15px;&quot;&gt;A72/R5F&lt;/font&gt;&lt;/b&gt;&lt;b&gt;&lt;font style=&quot;font-size: 15px;&quot;&gt;&amp;nbsp;→&amp;nbsp;&lt;/font&gt;&lt;/b&gt;&lt;b style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;font style=&quot;font-size: 15px;&quot;&gt;C7x&lt;/font&gt;&lt;/b&gt;&lt;/div&gt;&lt;ol style=&quot;line-height: 200%;&quot;&gt;&lt;li&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;The message is first copied from the application to VRING used between the two CPUs. After this the IPC driver posts the VRING ID in the HW mailbox.&lt;/span&gt;&lt;/li&gt;&lt;li&gt;This triggers a interrupt on the destination CPU. In the ISR of destination CPU, it extracts the VRING ID and then based on the VRING ID, checks for any messages in that VRING&lt;/li&gt;&lt;li&gt;If a message is received, it extracts the message from the VRING and puts it in the destination RPMSG endpoint queue. It then triggers the application blocked on this RPMSG endpoint&lt;/li&gt;&lt;li&gt;The application then handles the received message and replies back to the sender CPU using the same RPMSG and VRING mechanism in the reverse direction.&lt;/li&gt;&lt;/ol&gt;" style="text;html=1;align=left;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=#d79b00;fillColor=#ffe6cc;fontFamily=Helvetica;" parent="1" vertex="1">
          <mxGeometry x="40" y="1319" width="1037" height="150" as="geometry" />
        </mxCell>
        <mxCell id="a1OmAsUUNiDIbFJyHpuR-18" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;fontFamily=Helvetica;" parent="1" target="bFK34A6A18MXLZeqNGrH-2" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="311" y="764" as="sourcePoint" />
            <mxPoint x="498" y="638" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="a1OmAsUUNiDIbFJyHpuR-19" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(153, 204, 255);&quot;&gt;&lt;b style=&quot;&quot;&gt;(2)&lt;/b&gt;&lt;/font&gt;&amp;nbsp;start&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="a1OmAsUUNiDIbFJyHpuR-18" vertex="1" connectable="0">
          <mxGeometry x="0.5029" y="1" relative="1" as="geometry">
            <mxPoint x="-78" y="149" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="a1OmAsUUNiDIbFJyHpuR-20" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;fontFamily=Helvetica;" parent="1" target="bFK34A6A18MXLZeqNGrH-19" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1313" y="775" as="sourcePoint" />
            <mxPoint x="405" y="450" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="a1OmAsUUNiDIbFJyHpuR-21" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(153, 204, 255);&quot;&gt;&lt;b style=&quot;&quot;&gt;(2)&lt;/b&gt;&lt;/font&gt;&amp;nbsp;start&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="a1OmAsUUNiDIbFJyHpuR-20" vertex="1" connectable="0">
          <mxGeometry x="0.5029" y="1" relative="1" as="geometry">
            <mxPoint x="-15" y="46" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-3" value="C71x (Non QEMU process)" style="swimlane;whiteSpace=wrap;html=1;startSize=40;fontFamily=Helvetica;" parent="1" vertex="1">
          <mxGeometry x="2679" y="405" width="306" height="420" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="h9OmZHuHNuUbkvZkJMz0-3" source="h9OmZHuHNuUbkvZkJMz0-6" target="h9OmZHuHNuUbkvZkJMz0-18" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="201" y="276" as="sourcePoint" />
            <mxPoint x="201" y="196" as="targetPoint" />
            <Array as="points">
              <mxPoint x="275" y="295" />
              <mxPoint x="275" y="160" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-5" value="&lt;div&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(2.1)&lt;/font&gt;&lt;/div&gt;notify" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;fontSize=16;" parent="h9OmZHuHNuUbkvZkJMz0-4" connectable="0" vertex="1">
          <mxGeometry x="-0.0321" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-6" value="ivshmem-client" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="h9OmZHuHNuUbkvZkJMz0-3" vertex="1">
          <mxGeometry x="40" y="260" width="200" height="70" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;entryX=0.188;entryY=-0.014;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.143;exitY=0.975;exitDx=0;exitDy=0;exitPerimeter=0;" parent="h9OmZHuHNuUbkvZkJMz0-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="63.01999999999998" y="178.9999999999999" as="sourcePoint" />
            <mxPoint x="63.07999999999993" y="259.01999999999987" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-8" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(153, 204, 255);&quot;&gt;&lt;b style=&quot;&quot;&gt;(2)&lt;/b&gt;&lt;/font&gt;&amp;nbsp;Initialize&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="h9OmZHuHNuUbkvZkJMz0-7" connectable="0" vertex="1">
          <mxGeometry x="0.1" relative="1" as="geometry">
            <mxPoint y="6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;" parent="h9OmZHuHNuUbkvZkJMz0-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="201" y="180" as="sourcePoint" />
            <mxPoint x="202.02040816326553" y="260" as="targetPoint" />
            <Array as="points">
              <mxPoint x="201" y="220" />
              <mxPoint x="202" y="220" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-10" value="&lt;div&gt;&lt;font style=&quot;font-size: 16px; color: rgb(255, 153, 51);&quot;&gt;(2.2)&lt;/font&gt;&lt;font style=&quot;font-size: 16px; color: rgb(0, 255, 0);&quot;&gt;(2.1)&lt;/font&gt;&lt;/div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;vring&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="h9OmZHuHNuUbkvZkJMz0-9" connectable="0" vertex="1">
          <mxGeometry x="-0.1393" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0.363;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" parent="h9OmZHuHNuUbkvZkJMz0-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="121.59999999999991" y="180" as="sourcePoint" />
            <mxPoint x="129" y="260" as="targetPoint" />
            <Array as="points">
              <mxPoint x="129" y="180" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-12" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(3.1)&lt;/font&gt; kick&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="h9OmZHuHNuUbkvZkJMz0-11" connectable="0" vertex="1">
          <mxGeometry x="-0.1583" y="1" relative="1" as="geometry">
            <mxPoint y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="h9OmZHuHNuUbkvZkJMz0-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="64" y="100" as="sourcePoint" />
            <mxPoint x="64" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-14" value="&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(1)&lt;/font&gt; send msg" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=16;" parent="h9OmZHuHNuUbkvZkJMz0-13" connectable="0" vertex="1">
          <mxGeometry x="-0.08" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="h9OmZHuHNuUbkvZkJMz0-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="196" y="100" as="sourcePoint" />
            <mxPoint x="196" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-16" value="&lt;font style=&quot;font-size: 16px; color: rgb(255, 153, 51);&quot;&gt;(4)&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;recv msg&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="h9OmZHuHNuUbkvZkJMz0-15" connectable="0" vertex="1">
          <mxGeometry x="-0.3047" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-17" value="Application" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="h9OmZHuHNuUbkvZkJMz0-3" vertex="1">
          <mxGeometry x="40" y="60" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-18" value="IPC Driver" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="h9OmZHuHNuUbkvZkJMz0-3" vertex="1">
          <mxGeometry x="40" y="140" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-19" value="TDA4 SoC Machine (QEMU process)" style="swimlane;whiteSpace=wrap;html=1;startSize=40;fontFamily=Helvetica;" parent="1" vertex="1">
          <mxGeometry x="1693" y="425" width="732" height="359" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-22" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;" parent="h9OmZHuHNuUbkvZkJMz0-19" source="h9OmZHuHNuUbkvZkJMz0-24" target="h9OmZHuHNuUbkvZkJMz0-43" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-23" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;/font&gt;&lt;div&gt;&lt;span style=&quot;font-size: 16px; color: rgb(0, 255, 0);&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.4)&lt;/font&gt;&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 255, 0); font-size: 16px;&quot;&gt;(3.4)&lt;/span&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="h9OmZHuHNuUbkvZkJMz0-22" connectable="0" vertex="1">
          <mxGeometry x="0.1157" relative="1" as="geometry">
            <mxPoint x="5" y="3" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-24" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;ivshmem-client&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;(QEMU device)&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;(add handlers for other peerID req)&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="h9OmZHuHNuUbkvZkJMz0-19" vertex="1">
          <mxGeometry x="235" y="239" width="248" height="70" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-40" value="" style="group" parent="h9OmZHuHNuUbkvZkJMz0-19" connectable="0" vertex="1">
          <mxGeometry x="7" y="100" width="100" height="230" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-41" value="Memory" style="rounded=0;whiteSpace=wrap;html=1;align=center;labelPosition=center;verticalLabelPosition=top;verticalAlign=bottom;fontFamily=Helvetica;" parent="h9OmZHuHNuUbkvZkJMz0-40" vertex="1">
          <mxGeometry width="100" height="230" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-42" value="" style="group;fontFamily=Helvetica;" parent="h9OmZHuHNuUbkvZkJMz0-40" connectable="0" vertex="1">
          <mxGeometry y="80" width="100" height="120" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-43" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Share memory&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;(QEMU)&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="h9OmZHuHNuUbkvZkJMz0-42" vertex="1">
          <mxGeometry width="100" height="120" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-44" value="VRING-0" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="h9OmZHuHNuUbkvZkJMz0-42" vertex="1">
          <mxGeometry width="100" height="26.666666666666668" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-45" value="VRING-n" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="h9OmZHuHNuUbkvZkJMz0-42" vertex="1">
          <mxGeometry y="93.33333333333334" width="100" height="26.666666666666668" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-58" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="h9OmZHuHNuUbkvZkJMz0-19" source="h9OmZHuHNuUbkvZkJMz0-55" target="h9OmZHuHNuUbkvZkJMz0-24" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-59" value="Text" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="h9OmZHuHNuUbkvZkJMz0-58" connectable="0" vertex="1">
          <mxGeometry x="0.0383" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-61" value="Configure" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="h9OmZHuHNuUbkvZkJMz0-58" connectable="0" vertex="1">
          <mxGeometry x="0.0531" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-55" value="PCI-BAR support" style="rounded=1;whiteSpace=wrap;html=1;" parent="h9OmZHuHNuUbkvZkJMz0-19" vertex="1">
          <mxGeometry x="228" y="138" width="263" height="23" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-75" value="qemu-system-aarch64 -M virt -m 256M -cpu cortex-a72 -kernel linux-6.1.58/arch/arm64/boot/Image -initrd rootfs.cpio.gz -device ivshmem-doorbell,vectors=4,chardev=iv1 -chardev socket,path=/tmp/ivshmem.sock,id=iv1 -append &quot;root=/dev/mem&quot; -nographic" style="whiteSpace=wrap;html=1;" parent="h9OmZHuHNuUbkvZkJMz0-19" vertex="1">
          <mxGeometry x="102" y="309" width="630" height="50" as="geometry" />
        </mxCell>
        <mxCell id="Ayd5wQnlPEcSk8cYhDMF-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.281;entryY=0.957;entryDx=0;entryDy=0;entryPerimeter=0;" parent="h9OmZHuHNuUbkvZkJMz0-19" source="h9OmZHuHNuUbkvZkJMz0-24" target="h9OmZHuHNuUbkvZkJMz0-55" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-48" value="ivshmem-server process&lt;div&gt;(add with max vectors&lt;/div&gt;&lt;div&gt;-n max_vect)&lt;br&gt;&lt;br&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="1" vertex="1">
          <mxGeometry x="2324" y="924" width="342" height="80" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-49" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" target="h9OmZHuHNuUbkvZkJMz0-48" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="2050.9999999999995" y="732" as="sourcePoint" />
            <mxPoint x="2276" y="960" as="targetPoint" />
            <Array as="points">
              <mxPoint x="2051" y="964" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-51" value="Conn" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="h9OmZHuHNuUbkvZkJMz0-49" connectable="0" vertex="1">
          <mxGeometry x="0.1291" y="1" relative="1" as="geometry">
            <mxPoint x="55" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-50" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="h9OmZHuHNuUbkvZkJMz0-6" target="h9OmZHuHNuUbkvZkJMz0-48" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2847" y="964" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-52" value="Conn" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="h9OmZHuHNuUbkvZkJMz0-50" connectable="0" vertex="1">
          <mxGeometry x="0.3388" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-53" value="" style="edgeStyle=none;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;startArrow=classic;endSize=8;startSize=8;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="h9OmZHuHNuUbkvZkJMz0-24" target="h9OmZHuHNuUbkvZkJMz0-6" edge="1">
          <mxGeometry width="100" relative="1" as="geometry">
            <mxPoint x="2464" y="757" as="sourcePoint" />
            <mxPoint x="2564" y="757" as="targetPoint" />
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-54" value="Doorbell-ivshmem" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="2435" y="678" width="115" height="26" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-63" value="&lt;font style=&quot;font-size: 20px;&quot;&gt;HOST&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="2398" y="318" width="74" height="36" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-65" value="POSIX&lt;br&gt;Shared memory&lt;div&gt;/dev/shm/my_shmem&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="2495" y="411" width="108" height="160" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-67" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="h9OmZHuHNuUbkvZkJMz0-6" target="h9OmZHuHNuUbkvZkJMz0-65" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2719" y="672" />
              <mxPoint x="2638" y="672" />
              <mxPoint x="2638" y="491" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-70" value="R/W" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="h9OmZHuHNuUbkvZkJMz0-67" connectable="0" vertex="1">
          <mxGeometry x="-0.0029" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-71" value="./ivshmem-server -F -v -S /tmp/ivshmem.sock -m /dev/shm/ -M my_shmem -l 1M -n 4" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="2323" y="864" width="343" height="60" as="geometry" />
        </mxCell>
        <mxCell id="h9OmZHuHNuUbkvZkJMz0-74" value="./ivshmem-client -S /tmp/ivshmem.sock" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="2719" y="736" width="200" height="33" as="geometry" />
        </mxCell>
        <mxCell id="Ayd5wQnlPEcSk8cYhDMF-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.031;entryY=0.384;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="h9OmZHuHNuUbkvZkJMz0-55" target="h9OmZHuHNuUbkvZkJMz0-65" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram name="wip_Thangnd01" id="jVtXm5pZ91t34IUDPaG4">
    <mxGraphModel dx="1724" dy="814" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="1654" math="0" shadow="0">
      <root>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-0" />
        <mxCell id="GSmwUHLEAzuctVDBRrmO-1" parent="GSmwUHLEAzuctVDBRrmO-0" />
        <mxCell id="GSmwUHLEAzuctVDBRrmO-2" value="Host (x86-64)" style="swimlane;whiteSpace=wrap;html=1;startSize=40;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-1" vertex="1">
          <mxGeometry x="40" y="40" width="1411" height="800" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-3" value="TDA4 SoC Machine (QEMU process)" style="swimlane;whiteSpace=wrap;html=1;startSize=40;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-2" vertex="1">
          <mxGeometry x="20" y="60" width="727" height="340" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=1;entryDx=0;entryDy=0;fontFamily=Helvetica;exitX=0.291;exitY=0.013;exitDx=0;exitDy=0;exitPerimeter=0;" parent="GSmwUHLEAzuctVDBRrmO-3" source="GSmwUHLEAzuctVDBRrmO-8" target="GSmwUHLEAzuctVDBRrmO-15" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="370" y="200" as="sourcePoint" />
            <mxPoint x="370" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-5" value="read/write&lt;div&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(3.5)&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;fontSize=16;" parent="GSmwUHLEAzuctVDBRrmO-4" vertex="1" connectable="0">
          <mxGeometry x="-0.0143" y="1" relative="1" as="geometry">
            <mxPoint x="2" y="18" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-6" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-3" source="GSmwUHLEAzuctVDBRrmO-8" target="GSmwUHLEAzuctVDBRrmO-27" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-7" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;/font&gt;&lt;div&gt;&lt;span style=&quot;font-size: 16px; color: rgb(0, 255, 0);&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.4)&lt;/font&gt;&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 255, 0); font-size: 16px;&quot;&gt;(3.4)&lt;/span&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-6" vertex="1" connectable="0">
          <mxGeometry x="0.1157" relative="1" as="geometry">
            <mxPoint x="5" y="3" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-8" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;ivshmem-client&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;(QEMU device)&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-3" vertex="1">
          <mxGeometry x="320" y="260" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.75;entryDx=0;entryDy=0;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-3" target="GSmwUHLEAzuctVDBRrmO-18" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="340" y="157" as="sourcePoint" />
            <mxPoint x="260" y="157" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-10" value="notify (IRQ)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-9" vertex="1" connectable="0">
          <mxGeometry x="0.0298" relative="1" as="geometry">
            <mxPoint x="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;entryX=0;entryY=0.75;entryDx=0;entryDy=0;exitX=1;exitY=0.75;exitDx=0;exitDy=0;exitPerimeter=0;" parent="GSmwUHLEAzuctVDBRrmO-3" source="GSmwUHLEAzuctVDBRrmO-15" target="GSmwUHLEAzuctVDBRrmO-23" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="460" y="157" as="sourcePoint" />
            <mxPoint x="540" y="157" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-12" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;notify (IRQ)&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px; color: rgb(0, 255, 0);&quot;&gt;(4.1)&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-11" vertex="1" connectable="0">
          <mxGeometry x="-0.3393" y="1" relative="1" as="geometry">
            <mxPoint x="15" y="14" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0.75;exitY=1;exitDx=0;exitDy=0;" parent="GSmwUHLEAzuctVDBRrmO-3" source="GSmwUHLEAzuctVDBRrmO-15" target="GSmwUHLEAzuctVDBRrmO-8" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="429" y="140" as="sourcePoint" />
            <mxPoint x="429" y="200" as="targetPoint" />
            <Array as="points">
              <mxPoint x="426" y="239" />
              <mxPoint x="426" y="239" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-14" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;notify (IRQ)&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px; color: rgb(255, 153, 51);&quot;&gt;(1.3)&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-13" vertex="1" connectable="0">
          <mxGeometry x="-0.119" relative="1" as="geometry">
            <mxPoint y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-15" value="Mailbox model&lt;div&gt;(QEMU device)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-3" vertex="1">
          <mxGeometry x="336" y="97" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.25;exitDx=0;exitDy=0;fontFamily=Helvetica;entryX=0;entryY=0.25;entryDx=0;entryDy=0;" parent="GSmwUHLEAzuctVDBRrmO-3" source="GSmwUHLEAzuctVDBRrmO-18" target="GSmwUHLEAzuctVDBRrmO-15" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="280" y="117" as="sourcePoint" />
            <mxPoint x="340" y="117" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-17" value="read/write" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-16" vertex="1" connectable="0">
          <mxGeometry x="-0.6488" y="-1" relative="1" as="geometry">
            <mxPoint x="22" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-18" value="R5F&lt;div&gt;(QEMU device)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-3" vertex="1">
          <mxGeometry x="164" y="97" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0;exitY=0.25;exitDx=0;exitDy=0;entryX=0.98;entryY=0.26;entryDx=0;entryDy=0;entryPerimeter=0;" parent="GSmwUHLEAzuctVDBRrmO-3" source="GSmwUHLEAzuctVDBRrmO-23" target="GSmwUHLEAzuctVDBRrmO-15" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="540" y="118" as="sourcePoint" />
            <mxPoint x="460" y="118" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-20" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.2)&lt;/font&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(4.2)&lt;/font&gt;&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-19" vertex="1" connectable="0">
          <mxGeometry x="0.256" y="-1" relative="1" as="geometry">
            <mxPoint x="15" y="-11" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;curved=1;exitX=0.25;exitY=0;exitDx=0;exitDy=0;" parent="GSmwUHLEAzuctVDBRrmO-3" source="GSmwUHLEAzuctVDBRrmO-23" target="GSmwUHLEAzuctVDBRrmO-25" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="590" y="97" />
              <mxPoint x="590" y="60" />
              <mxPoint x="95" y="60" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-22" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.1)&lt;/font&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(5)&lt;/font&gt; read/write&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="GSmwUHLEAzuctVDBRrmO-21" vertex="1" connectable="0">
          <mxGeometry x="0.1318" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-23" value="A72&lt;div&gt;(QEMU device)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-3" vertex="1">
          <mxGeometry x="595" y="97" width="100" height="80" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-24" value="" style="group" parent="GSmwUHLEAzuctVDBRrmO-3" vertex="1" connectable="0">
          <mxGeometry x="7" y="100" width="100" height="230" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-25" value="Memory" style="rounded=0;whiteSpace=wrap;html=1;align=center;labelPosition=center;verticalLabelPosition=top;verticalAlign=bottom;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-24" vertex="1">
          <mxGeometry width="100" height="230" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-26" value="" style="group;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-24" vertex="1" connectable="0">
          <mxGeometry y="80" width="100" height="120" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-27" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Share memory&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;(QEMU)&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-26" vertex="1">
          <mxGeometry width="100" height="120" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-28" value="VRING-0" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-26" vertex="1">
          <mxGeometry width="100" height="26.666666666666668" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-29" value="VRING-n" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-26" vertex="1">
          <mxGeometry y="93.33333333333334" width="100" height="26.666666666666668" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-30" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1.01;entryY=0.302;entryDx=0;entryDy=0;entryPerimeter=0;" parent="GSmwUHLEAzuctVDBRrmO-3" source="GSmwUHLEAzuctVDBRrmO-18" target="GSmwUHLEAzuctVDBRrmO-25" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-31" value="read/write" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="GSmwUHLEAzuctVDBRrmO-30" vertex="1" connectable="0">
          <mxGeometry x="0.0347" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-32" value="C71x (Non QEMU process)" style="swimlane;whiteSpace=wrap;html=1;startSize=40;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-2" vertex="1">
          <mxGeometry x="1040" y="60" width="306" height="420" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="GSmwUHLEAzuctVDBRrmO-32" source="GSmwUHLEAzuctVDBRrmO-35" target="GSmwUHLEAzuctVDBRrmO-47" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="201" y="276" as="sourcePoint" />
            <mxPoint x="201" y="196" as="targetPoint" />
            <Array as="points">
              <mxPoint x="275" y="295" />
              <mxPoint x="275" y="160" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-34" value="&lt;div&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(2.1)&lt;/font&gt;&lt;/div&gt;notify" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;fontSize=16;" parent="GSmwUHLEAzuctVDBRrmO-33" vertex="1" connectable="0">
          <mxGeometry x="-0.0321" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-35" value="ivshmem-client" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-32" vertex="1">
          <mxGeometry x="40" y="260" width="200" height="70" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;entryX=0.188;entryY=-0.014;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.143;exitY=0.975;exitDx=0;exitDy=0;exitPerimeter=0;" parent="GSmwUHLEAzuctVDBRrmO-32" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="63.01999999999998" y="178.9999999999999" as="sourcePoint" />
            <mxPoint x="63.07999999999993" y="259.01999999999987" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-37" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(153, 204, 255);&quot;&gt;&lt;b style=&quot;&quot;&gt;(2)&lt;/b&gt;&lt;/font&gt;&amp;nbsp;Initialize&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-36" vertex="1" connectable="0">
          <mxGeometry x="0.1" relative="1" as="geometry">
            <mxPoint y="6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-38" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-32" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="201" y="180" as="sourcePoint" />
            <mxPoint x="202.02040816326553" y="260" as="targetPoint" />
            <Array as="points">
              <mxPoint x="201" y="220" />
              <mxPoint x="202" y="220" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-39" value="&lt;div&gt;&lt;font style=&quot;font-size: 16px; color: rgb(255, 153, 51);&quot;&gt;(2.2)&lt;/font&gt;&lt;font style=&quot;font-size: 16px; color: rgb(0, 255, 0);&quot;&gt;(2.1)&lt;/font&gt;&lt;/div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;vring&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-38" vertex="1" connectable="0">
          <mxGeometry x="-0.1393" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-40" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0.363;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" parent="GSmwUHLEAzuctVDBRrmO-32" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="121.59999999999991" y="180" as="sourcePoint" />
            <mxPoint x="129" y="260" as="targetPoint" />
            <Array as="points">
              <mxPoint x="129" y="180" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-41" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(3.1)&lt;/font&gt; kick&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-40" vertex="1" connectable="0">
          <mxGeometry x="-0.1583" y="1" relative="1" as="geometry">
            <mxPoint y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-42" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="GSmwUHLEAzuctVDBRrmO-32" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="64" y="100" as="sourcePoint" />
            <mxPoint x="64" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-43" value="&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(1)&lt;/font&gt; send msg" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=16;" parent="GSmwUHLEAzuctVDBRrmO-42" vertex="1" connectable="0">
          <mxGeometry x="-0.08" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-44" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="GSmwUHLEAzuctVDBRrmO-32" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="196" y="100" as="sourcePoint" />
            <mxPoint x="196" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-45" value="&lt;font style=&quot;font-size: 16px; color: rgb(255, 153, 51);&quot;&gt;(4)&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;recv msg&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="GSmwUHLEAzuctVDBRrmO-44" vertex="1" connectable="0">
          <mxGeometry x="-0.3047" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-46" value="Application" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-32" vertex="1">
          <mxGeometry x="40" y="60" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-47" value="IPC Driver" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-32" vertex="1">
          <mxGeometry x="40" y="140" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-48" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="GSmwUHLEAzuctVDBRrmO-2" source="GSmwUHLEAzuctVDBRrmO-50" target="GSmwUHLEAzuctVDBRrmO-64" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="830" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-49" value="&lt;font style=&quot;font-size: 16px; color: rgb(153, 204, 255);&quot;&gt;&lt;b&gt;(1)&lt;/b&gt;&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;initialize&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="GSmwUHLEAzuctVDBRrmO-48" vertex="1" connectable="0">
          <mxGeometry x="-0.1061" y="-1" relative="1" as="geometry">
            <mxPoint x="-1" y="-34" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-50" value="ivshmem-server process" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-2" vertex="1">
          <mxGeometry x="657" y="616" width="240" height="80" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-51" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-2" source="GSmwUHLEAzuctVDBRrmO-35" target="GSmwUHLEAzuctVDBRrmO-50" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1180" y="450" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-52" value="&lt;font style=&quot;font-size: 16px; color: rgb(153, 204, 255);&quot;&gt;&lt;b&gt;(2)&lt;/b&gt;&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;connect&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-51" vertex="1" connectable="0">
          <mxGeometry x="0.4148" relative="1" as="geometry">
            <mxPoint x="-4" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-53" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-2" source="GSmwUHLEAzuctVDBRrmO-8" target="GSmwUHLEAzuctVDBRrmO-50" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="440" y="450" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-54" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(153, 204, 255);&quot;&gt;&lt;b style=&quot;&quot;&gt;(2)&lt;/b&gt;&lt;/font&gt; connect&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-53" vertex="1" connectable="0">
          <mxGeometry x="0.5029" y="1" relative="1" as="geometry">
            <mxPoint x="-20" y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-55" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;startArrow=classic;startFill=1;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-2" source="GSmwUHLEAzuctVDBRrmO-8" target="GSmwUHLEAzuctVDBRrmO-35" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-56" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;span style=&quot;color: rgb(0, 255, 0);&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.6)&lt;/font&gt;&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 255, 0);&quot;&gt;(3.2)&amp;nbsp;&lt;/span&gt;Notify &quot;VRING ID&quot;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-55" vertex="1" connectable="0">
          <mxGeometry x="-0.1593" y="1" relative="1" as="geometry">
            <mxPoint x="-77" y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-57" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1.011;entryY=0.679;entryDx=0;entryDy=0;entryPerimeter=0;fontFamily=Helvetica;exitX=0;exitY=0;exitDx=0;exitDy=0;" parent="GSmwUHLEAzuctVDBRrmO-2" source="GSmwUHLEAzuctVDBRrmO-35" target="GSmwUHLEAzuctVDBRrmO-62" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-58" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(3)&lt;/font&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(2.2)&lt;/font&gt;&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="GSmwUHLEAzuctVDBRrmO-57" vertex="1" connectable="0">
          <mxGeometry x="0.0091" y="-1" relative="1" as="geometry">
            <mxPoint x="-19" y="-10" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-59" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-2" source="GSmwUHLEAzuctVDBRrmO-8" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="799" y="212" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-60" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;br&gt;&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;span style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); color: rgb(0, 255, 0);&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.5)&lt;/font&gt;&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 255, 0); background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));&quot;&gt;(3.3)&lt;/span&gt;&amp;nbsp;&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-59" vertex="1" connectable="0">
          <mxGeometry x="0.0258" y="2" relative="1" as="geometry">
            <mxPoint x="-32" y="17" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-61" value="" style="group;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-2" vertex="1" connectable="0">
          <mxGeometry x="800" y="60" width="120" height="270" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-62" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Shared memory&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;(Host)&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-61" vertex="1">
          <mxGeometry width="120" height="270" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-63" value="VRING-0" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-61" vertex="1">
          <mxGeometry width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-64" value="VRING-n" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-61" vertex="1">
          <mxGeometry y="140" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-65" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="GSmwUHLEAzuctVDBRrmO-2" target="GSmwUHLEAzuctVDBRrmO-50" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="805" y="835" as="sourcePoint" />
            <mxPoint x="870" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-66" value="&lt;font style=&quot;font-size: 16px; color: rgb(153, 204, 255);&quot;&gt;&lt;b&gt;(1)&lt;/b&gt;&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;start&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="GSmwUHLEAzuctVDBRrmO-65" vertex="1" connectable="0">
          <mxGeometry x="-0.1061" y="-1" relative="1" as="geometry">
            <mxPoint x="-6" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-70" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-1" target="GSmwUHLEAzuctVDBRrmO-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="298" y="612" as="sourcePoint" />
            <mxPoint x="498" y="638" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-71" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(153, 204, 255);&quot;&gt;&lt;b style=&quot;&quot;&gt;(2)&lt;/b&gt;&lt;/font&gt;&amp;nbsp;start&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-70" vertex="1" connectable="0">
          <mxGeometry x="0.5029" y="1" relative="1" as="geometry">
            <mxPoint x="-57" y="104" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-72" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-1" target="GSmwUHLEAzuctVDBRrmO-32" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1185" y="631" as="sourcePoint" />
            <mxPoint x="405" y="450" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GSmwUHLEAzuctVDBRrmO-73" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(153, 204, 255);&quot;&gt;&lt;b style=&quot;&quot;&gt;(2)&lt;/b&gt;&lt;/font&gt;&amp;nbsp;start&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-72" vertex="1" connectable="0">
          <mxGeometry x="0.5029" y="1" relative="1" as="geometry">
            <mxPoint x="-15" y="46" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-0" value="Host (x86-64)" style="swimlane;whiteSpace=wrap;html=1;startSize=40;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-1" vertex="1">
          <mxGeometry x="112" y="1510" width="1238" height="557" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-30" value="Core0 Process" style="swimlane;whiteSpace=wrap;html=1;startSize=40;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-0" vertex="1">
          <mxGeometry x="668" y="58" width="522" height="420" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-33" value="ivshmem-client" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-30" vertex="1">
          <mxGeometry x="20" y="299" width="460" height="81" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-30" source="mo5cNOL35KpRXPZzGjNk-45" target="mo5cNOL35KpRXPZzGjNk-33" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="63.01999999999998" y="178.9999999999999" as="sourcePoint" />
            <mxPoint x="63.07999999999993" y="259.01999999999987" as="targetPoint" />
            <Array as="points">
              <mxPoint x="80" y="259" />
              <mxPoint x="80" y="259" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-35" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font&gt;&lt;font style=&quot;color: rgb(0, 127, 255);&quot;&gt;(2.3)&lt;/font&gt;&lt;b style=&quot;color: rgb(0, 127, 255);&quot;&gt;&amp;nbsp;&lt;/b&gt;&lt;/font&gt;Initialize()&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-34" vertex="1" connectable="0">
          <mxGeometry x="0.1" relative="1" as="geometry">
            <mxPoint y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-38" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="mo5cNOL35KpRXPZzGjNk-30" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="287.9999999999998" y="219" as="sourcePoint" />
            <mxPoint x="287.9999999999998" y="299" as="targetPoint" />
            <Array as="points">
              <mxPoint x="288" y="249" />
              <mxPoint x="288" y="249" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-39" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(3.1)&lt;/font&gt; kick&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-38" vertex="1" connectable="0">
          <mxGeometry x="-0.1583" y="1" relative="1" as="geometry">
            <mxPoint y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-40" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="mo5cNOL35KpRXPZzGjNk-30" source="mo5cNOL35KpRXPZzGjNk-44" target="mo5cNOL35KpRXPZzGjNk-45" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="64" y="100" as="sourcePoint" />
            <mxPoint x="64" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-41" value="&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(1.1)&lt;/font&gt;&amp;nbsp;Rpmsg_send()" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=16;" parent="mo5cNOL35KpRXPZzGjNk-40" vertex="1" connectable="0">
          <mxGeometry x="-0.08" relative="1" as="geometry">
            <mxPoint y="-4" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-42" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.914;entryY=0.039;entryDx=0;entryDy=0;exitX=0.914;exitY=1.004;exitDx=0;exitDy=0;exitPerimeter=0;entryPerimeter=0;" parent="mo5cNOL35KpRXPZzGjNk-30" source="mo5cNOL35KpRXPZzGjNk-44" target="mo5cNOL35KpRXPZzGjNk-45" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="257" y="100" as="sourcePoint" />
            <mxPoint x="257" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-43" value="&lt;font style=&quot;font-size: 16px; color: rgb(0, 255, 0);&quot;&gt;(5.2)&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;Rpmsg_recv()&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="mo5cNOL35KpRXPZzGjNk-42" vertex="1" connectable="0">
          <mxGeometry x="-0.3047" relative="1" as="geometry">
            <mxPoint x="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-44" value="Application" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-30" vertex="1">
          <mxGeometry x="20" y="60" width="460" height="40" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-94" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="mo5cNOL35KpRXPZzGjNk-30" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="197" y="219" as="sourcePoint" />
            <mxPoint x="197" y="298.9999999999998" as="targetPoint" />
            <Array as="points">
              <mxPoint x="197" y="259" />
              <mxPoint x="197" y="259" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-95" value="&lt;div&gt;&lt;font style=&quot;font-size: 16px; color: rgb(0, 255, 0);&quot;&gt;(2.1)&lt;/font&gt;&lt;/div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;write&amp;nbsp;&lt;/font&gt;&lt;span style=&quot;font-size: 16px; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;vring&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="mo5cNOL35KpRXPZzGjNk-94" vertex="1" connectable="0">
          <mxGeometry x="-0.05" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-45" value="IPC Driver" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-30" vertex="1">
          <mxGeometry x="20" y="179" width="460" height="40" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-86" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.129;exitY=0.993;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.131;entryY=-0.016;entryDx=0;entryDy=0;entryPerimeter=0;" parent="mo5cNOL35KpRXPZzGjNk-30" source="mo5cNOL35KpRXPZzGjNk-44" target="mo5cNOL35KpRXPZzGjNk-45" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="80" y="139" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-87" value="&lt;font style=&quot;font-size: 16px; color: rgb(0, 127, 255);&quot;&gt;(2.2)&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;Ipc_initVirtIO()&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="mo5cNOL35KpRXPZzGjNk-86" vertex="1" connectable="0">
          <mxGeometry x="-0.1376" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-98" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0.871;exitY=-0.004;exitDx=0;exitDy=0;entryX=0.869;entryY=1.007;entryDx=0;entryDy=0;entryPerimeter=0;exitPerimeter=0;" parent="mo5cNOL35KpRXPZzGjNk-30" source="mo5cNOL35KpRXPZzGjNk-33" target="mo5cNOL35KpRXPZzGjNk-45" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="297.9999999999998" y="229" as="sourcePoint" />
            <mxPoint x="297.9999999999998" y="309" as="targetPoint" />
            <Array as="points">
              <mxPoint x="420" y="299" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-99" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(5.1) &lt;/font&gt;&lt;font&gt;Put msg&lt;/font&gt;&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font&gt;to Rpmsg queue&lt;/font&gt;&amp;nbsp;&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-98" vertex="1" connectable="0">
          <mxGeometry x="-0.1583" y="1" relative="1" as="geometry">
            <mxPoint y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-46" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="mo5cNOL35KpRXPZzGjNk-0" source="mo5cNOL35KpRXPZzGjNk-48" target="mo5cNOL35KpRXPZzGjNk-62" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-47" value="&lt;font style=&quot;font-size: 16px; color: rgb(0, 127, 255);&quot;&gt;(1.2)&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;initialize&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="mo5cNOL35KpRXPZzGjNk-46" vertex="1" connectable="0">
          <mxGeometry x="-0.1061" y="-1" relative="1" as="geometry">
            <mxPoint x="-1" y="-34" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-48" value="ivshmem-server process" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-0" vertex="1">
          <mxGeometry x="348" y="467" width="240" height="80" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-49" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="mo5cNOL35KpRXPZzGjNk-0" source="mo5cNOL35KpRXPZzGjNk-33" target="mo5cNOL35KpRXPZzGjNk-48" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="918" y="517" />
            </Array>
            <mxPoint x="567" y="437" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-50" value="&lt;font style=&quot;font-size: 16px; color: rgb(0, 127, 255);&quot;&gt;(2.4)&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;connect&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-49" vertex="1" connectable="0">
          <mxGeometry x="0.4148" relative="1" as="geometry">
            <mxPoint x="17" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-55" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1.011;entryY=0.679;entryDx=0;entryDy=0;entryPerimeter=0;fontFamily=Helvetica;exitX=0;exitY=0;exitDx=0;exitDy=0;" parent="mo5cNOL35KpRXPZzGjNk-0" source="mo5cNOL35KpRXPZzGjNk-33" target="mo5cNOL35KpRXPZzGjNk-60" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-56" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(2.2)&amp;nbsp;&lt;/font&gt;&lt;/font&gt;&lt;span style=&quot;font-size: 16px; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;write&lt;/span&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(5.1)&amp;nbsp;&lt;/font&gt;&lt;/font&gt;&lt;span style=&quot;color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); font-size: 16px;&quot;&gt;read&lt;/span&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="mo5cNOL35KpRXPZzGjNk-55" vertex="1" connectable="0">
          <mxGeometry x="0.0091" y="-1" relative="1" as="geometry">
            <mxPoint x="-19" y="-10" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-59" value="" style="group;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-0" vertex="1" connectable="0">
          <mxGeometry x="386" y="60" width="120" height="180" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-60" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;VRING&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Shared memory&amp;nbsp;&lt;/span&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;(Host)&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-59" vertex="1">
          <mxGeometry width="120" height="180" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-61" value="VRING-0" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-59" vertex="1">
          <mxGeometry width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-62" value="VRING-n" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-59" vertex="1">
          <mxGeometry y="140" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-63" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="mo5cNOL35KpRXPZzGjNk-0" target="mo5cNOL35KpRXPZzGjNk-48" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="508" y="597" as="sourcePoint" />
            <mxPoint x="660" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-64" value="&lt;font style=&quot;font-size: 16px; color: rgb(0, 127, 255);&quot;&gt;(1.1)&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;start&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="mo5cNOL35KpRXPZzGjNk-63" vertex="1" connectable="0">
          <mxGeometry x="-0.1061" y="-1" relative="1" as="geometry">
            <mxPoint x="-6" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-51" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="mo5cNOL35KpRXPZzGjNk-0" source="mo5cNOL35KpRXPZzGjNk-92" target="mo5cNOL35KpRXPZzGjNk-48" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="144" y="507" />
            </Array>
            <mxPoint x="-10" y="390" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-52" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;connect&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-51" vertex="1" connectable="0">
          <mxGeometry x="0.5029" y="1" relative="1" as="geometry">
            <mxPoint x="-34" y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-53" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=block;startFill=1;fontFamily=Helvetica;endArrow=none;" parent="mo5cNOL35KpRXPZzGjNk-0" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="250" y="375.5" as="sourcePoint" />
            <mxPoint x="688" y="374.5" as="targetPoint" />
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-54" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;span style=&quot;color: rgb(0, 255, 0);&quot;&gt;(3.2)&amp;nbsp;&lt;/span&gt;Notify &quot;VRING ID&quot;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-53" vertex="1" connectable="0">
          <mxGeometry x="-0.1593" y="1" relative="1" as="geometry">
            <mxPoint x="134" y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-92" value="ivshmem-client" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-0" vertex="1">
          <mxGeometry x="38" y="357" width="212" height="83" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-96" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=none;startFill=0;fontFamily=Helvetica;endArrow=classic;endFill=1;" parent="mo5cNOL35KpRXPZzGjNk-0" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="250" y="418" as="sourcePoint" />
            <mxPoint x="688" y="417" as="targetPoint" />
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-97" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;span style=&quot;color: rgb(0, 255, 0);&quot;&gt;(4.1)&amp;nbsp;&lt;/span&gt;Receive &quot;VRING ID&quot;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-96" vertex="1" connectable="0">
          <mxGeometry x="-0.1593" y="1" relative="1" as="geometry">
            <mxPoint x="134" y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-90" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.75;entryY=1;entryDx=0;entryDy=0;" parent="mo5cNOL35KpRXPZzGjNk-0" target="mo5cNOL35KpRXPZzGjNk-30" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="868" y="597" as="sourcePoint" />
            <mxPoint x="867" y="55" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-91" value="&lt;font style=&quot;font-size: 16px; color: rgb(0, 127, 255);&quot;&gt;(2.1)&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;start&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="mo5cNOL35KpRXPZzGjNk-90" vertex="1" connectable="0">
          <mxGeometry x="-0.1061" y="-1" relative="1" as="geometry">
            <mxPoint x="-6" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-65" value="I want to replace shared ddr space with shared memory linux which created by ivshmem-server to act as VRING. Each core contain 1 ivshmem-client support read/write VRING; kicktrigger interrupt" style="text;whiteSpace=wrap;html=1;" parent="GSmwUHLEAzuctVDBRrmO-1" vertex="1">
          <mxGeometry x="885" y="944" width="473" height="51" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-66" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;b&gt;UC1_SC0: Initialization &lt;br&gt;&lt;/b&gt;&lt;/font&gt;Start ivshmem-server process. Server initializes a shared memory on Host for exchanging VRING data between processes. &lt;br&gt;Start QEMU and non-QEMU process. ivshmem-client connect to server. The server responds to the client with the information: clientID, other client fd, shared memory fd" style="text;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="GSmwUHLEAzuctVDBRrmO-1" vertex="1">
          <mxGeometry x="1480" y="40" width="467" height="94" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-67" value="C7x → A72/R5F &#xa;A application sends a message to a given destination (CPU, endpoint). &#xa;The message is first copied from the application to VRING used between the two CPUs. &#xa;After this the IPC driver posts the VRING ID in the HW mailbox. &#xa;This triggers a interrupt on the destination CPU. In the ISR of destination CPU, it extracts the VRING ID and then based on the VRING ID, checks for any messages in that VRING &#xa;If a message is received, it extracts the message from the VRING and puts it in the destination RPMSG endpoint queue. It then triggers the application blocked on this RPMSG endpoint" style="text;whiteSpace=wrap;html=1;" parent="GSmwUHLEAzuctVDBRrmO-1" vertex="1">
          <mxGeometry x="2690" y="920" width="550" height="137" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-68" value="A72/R5F → C7x &#xa;The message is first copied from the application to VRING used between the two CPUs. After this the IPC driver posts the VRING ID in the HW mailbox. &#xa;This triggers a interrupt on the destination CPU. In the ISR of destination CPU, it extracts the VRING ID and then based on the VRING ID, checks for any messages in that VRING &#xa;If a message is received, it extracts the message from the VRING and puts it in the destination RPMSG endpoint queue. It then triggers the application blocked on this RPMSG endpoint &#xa;The application then handles the received message and replies back to the sender CPU using the same RPMSG and VRING mechanism in the reverse direction." style="text;whiteSpace=wrap;html=1;" parent="GSmwUHLEAzuctVDBRrmO-1" vertex="1">
          <mxGeometry x="2000" y="960" width="550" height="152" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-1" value="TDA4 SoC Machine (QEMU process)" style="swimlane;whiteSpace=wrap;html=1;startSize=40;fontFamily=Helvetica;" parent="GSmwUHLEAzuctVDBRrmO-1" vertex="1">
          <mxGeometry x="119.5" y="2400" width="727" height="340" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=1;entryDx=0;entryDy=0;fontFamily=Helvetica;exitX=0.291;exitY=0.013;exitDx=0;exitDy=0;exitPerimeter=0;" parent="mo5cNOL35KpRXPZzGjNk-1" source="mo5cNOL35KpRXPZzGjNk-6" target="mo5cNOL35KpRXPZzGjNk-13" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="370" y="200" as="sourcePoint" />
            <mxPoint x="370" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-3" value="read/write&lt;div&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(3.5)&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;fontSize=16;" parent="mo5cNOL35KpRXPZzGjNk-2" vertex="1" connectable="0">
          <mxGeometry x="-0.0143" y="1" relative="1" as="geometry">
            <mxPoint x="2" y="18" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-4" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-1" source="mo5cNOL35KpRXPZzGjNk-6" target="mo5cNOL35KpRXPZzGjNk-25" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-5" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;/font&gt;&lt;div&gt;&lt;span style=&quot;font-size: 16px; color: rgb(0, 255, 0);&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.4)&lt;/font&gt;&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 255, 0); font-size: 16px;&quot;&gt;(3.4)&lt;/span&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-4" vertex="1" connectable="0">
          <mxGeometry x="0.1157" relative="1" as="geometry">
            <mxPoint x="5" y="3" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-6" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;ivshmem-client&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;(QEMU device)&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-1" vertex="1">
          <mxGeometry x="320" y="260" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.75;entryDx=0;entryDy=0;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-1" target="mo5cNOL35KpRXPZzGjNk-16" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="340" y="157" as="sourcePoint" />
            <mxPoint x="260" y="157" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-8" value="notify (IRQ)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-7" vertex="1" connectable="0">
          <mxGeometry x="0.0298" relative="1" as="geometry">
            <mxPoint x="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;entryX=0;entryY=0.75;entryDx=0;entryDy=0;exitX=1;exitY=0.75;exitDx=0;exitDy=0;exitPerimeter=0;" parent="mo5cNOL35KpRXPZzGjNk-1" source="mo5cNOL35KpRXPZzGjNk-13" target="mo5cNOL35KpRXPZzGjNk-21" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="460" y="157" as="sourcePoint" />
            <mxPoint x="540" y="157" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-10" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;notify (IRQ)&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px; color: rgb(0, 255, 0);&quot;&gt;(4.1)&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-9" vertex="1" connectable="0">
          <mxGeometry x="-0.3393" y="1" relative="1" as="geometry">
            <mxPoint x="15" y="14" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0.75;exitY=1;exitDx=0;exitDy=0;" parent="mo5cNOL35KpRXPZzGjNk-1" source="mo5cNOL35KpRXPZzGjNk-13" target="mo5cNOL35KpRXPZzGjNk-6" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="429" y="140" as="sourcePoint" />
            <mxPoint x="429" y="200" as="targetPoint" />
            <Array as="points">
              <mxPoint x="426" y="239" />
              <mxPoint x="426" y="239" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-12" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;notify (IRQ)&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px; color: rgb(255, 153, 51);&quot;&gt;(1.3)&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-11" vertex="1" connectable="0">
          <mxGeometry x="-0.119" relative="1" as="geometry">
            <mxPoint y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-13" value="Mailbox model&lt;div&gt;(QEMU device)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-1" vertex="1">
          <mxGeometry x="336" y="97" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.25;exitDx=0;exitDy=0;fontFamily=Helvetica;entryX=0;entryY=0.25;entryDx=0;entryDy=0;" parent="mo5cNOL35KpRXPZzGjNk-1" source="mo5cNOL35KpRXPZzGjNk-16" target="mo5cNOL35KpRXPZzGjNk-13" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="280" y="117" as="sourcePoint" />
            <mxPoint x="340" y="117" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-15" value="read/write" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-14" vertex="1" connectable="0">
          <mxGeometry x="-0.6488" y="-1" relative="1" as="geometry">
            <mxPoint x="22" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-16" value="R5F&lt;div&gt;(QEMU device)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-1" vertex="1">
          <mxGeometry x="164" y="97" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0;exitY=0.25;exitDx=0;exitDy=0;entryX=0.98;entryY=0.26;entryDx=0;entryDy=0;entryPerimeter=0;" parent="mo5cNOL35KpRXPZzGjNk-1" source="mo5cNOL35KpRXPZzGjNk-21" target="mo5cNOL35KpRXPZzGjNk-13" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="540" y="118" as="sourcePoint" />
            <mxPoint x="460" y="118" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-18" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.2)&lt;/font&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(4.2)&lt;/font&gt;&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-17" vertex="1" connectable="0">
          <mxGeometry x="0.256" y="-1" relative="1" as="geometry">
            <mxPoint x="15" y="-11" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;curved=1;exitX=0.25;exitY=0;exitDx=0;exitDy=0;" parent="mo5cNOL35KpRXPZzGjNk-1" source="mo5cNOL35KpRXPZzGjNk-21" target="mo5cNOL35KpRXPZzGjNk-23" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="590" y="97" />
              <mxPoint x="590" y="60" />
              <mxPoint x="95" y="60" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-20" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.1)&lt;/font&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(5)&lt;/font&gt; read/write&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="mo5cNOL35KpRXPZzGjNk-19" vertex="1" connectable="0">
          <mxGeometry x="0.1318" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-21" value="A72&lt;div&gt;(QEMU device)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-1" vertex="1">
          <mxGeometry x="595" y="97" width="100" height="80" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-22" value="" style="group" parent="mo5cNOL35KpRXPZzGjNk-1" vertex="1" connectable="0">
          <mxGeometry x="7" y="100" width="100" height="230" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-23" value="Memory" style="rounded=0;whiteSpace=wrap;html=1;align=center;labelPosition=center;verticalLabelPosition=top;verticalAlign=bottom;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-22" vertex="1">
          <mxGeometry width="100" height="230" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-24" value="" style="group;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-22" vertex="1" connectable="0">
          <mxGeometry y="80" width="100" height="120" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-25" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Share memory&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;(QEMU)&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-24" vertex="1">
          <mxGeometry width="100" height="120" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-26" value="VRING-0" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-24" vertex="1">
          <mxGeometry width="100" height="26.666666666666668" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-27" value="VRING-n" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="mo5cNOL35KpRXPZzGjNk-24" vertex="1">
          <mxGeometry y="93.33333333333334" width="100" height="26.666666666666668" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-28" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1.01;entryY=0.302;entryDx=0;entryDy=0;entryPerimeter=0;" parent="mo5cNOL35KpRXPZzGjNk-1" source="mo5cNOL35KpRXPZzGjNk-16" target="mo5cNOL35KpRXPZzGjNk-23" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-29" value="read/write" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="mo5cNOL35KpRXPZzGjNk-28" vertex="1" connectable="0">
          <mxGeometry x="0.0347" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-85" value="Initialization&lt;br&gt;&lt;br&gt;1. Ivshmem-server initialization&lt;div&gt;1.1 UI App s&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;tarts ivshmem-server process.&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;1.2 Server creates a shared memory on Host for exchanging VRING data between processes.&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;div&gt;2.&amp;nbsp;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Ivshmem-client initialization&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;C7x Host emulation&lt;/span&gt;&lt;/div&gt;&lt;div&gt;2.1 UI App starts C7x process.&lt;/div&gt;&lt;div&gt;2.2 Application call TI Driver API to initialize virtio.&lt;/div&gt;&lt;div&gt;2.3 IPC Driver initialize ivshmem-client thread.&lt;/div&gt;&lt;div&gt;2.4 ivshmem-client connect to server. The server responds to the client with the information: clientID, other client fd, VRING shared memory fd&lt;/div&gt;&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;TDA4 SoC Machine&lt;/div&gt;&lt;div&gt;&lt;div&gt;2.1 UI App starts TDA4 QEMU process.&lt;/div&gt;&lt;/div&gt;&lt;div&gt;2.2 ivshmem-client (QEMU device) is created. Then connect to server&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="GSmwUHLEAzuctVDBRrmO-1" vertex="1">
          <mxGeometry x="1990" y="640" width="467" height="270" as="geometry" />
        </mxCell>
        <mxCell id="mo5cNOL35KpRXPZzGjNk-93" value="Send message&lt;div&gt;1. A application sends a message to a given destination (CPU, endpoint).&lt;br&gt;1.1 Application call RPMessage_send() API&lt;br&gt;2. The message is first copied from the application to VRING used between the two CPUs.&lt;div&gt;2.1 IPC Driver request ivshmem-client write message to VRING shared memory.&lt;/div&gt;&lt;div&gt;3. After this the IPC driver posts the VRING ID in the HW mailbox.&lt;/div&gt;&lt;div&gt;3.1 IPC Driver request ivshmem-client to kick (notify) other client with VRING ID&lt;/div&gt;&lt;div&gt;3.2 ivshmem-client notify corresponding client by file descriptor&lt;br&gt;4. This triggers a interrupt on the destination CPU. In the ISR of destination CPU, it extracts the VRING ID and then based on the VRING ID, checks for any messages in that VRING&lt;/div&gt;&lt;div&gt;4.1 ivshmem-client receives notification. It gets VRING ID then checks for any messages in that VRING&lt;br&gt;5. If a message is received, it extracts the message from the VRING and puts it in the destination RPMSG endpoint queue. It then triggers the application blocked on this RPMSG endpoint&lt;/div&gt;&lt;div&gt;5.1 ivshmem-client reads message from VRING then puts it in the&amp;nbsp;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;destination RPMSG endpoint queue&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;5.2&amp;nbsp;&lt;/span&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;It then triggers the application blocked on this RPMSG endpoint&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" parent="GSmwUHLEAzuctVDBRrmO-1" vertex="1">
          <mxGeometry x="2690" y="650" width="550" height="240" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-65" value="Host (x86-64)" style="swimlane;whiteSpace=wrap;html=1;startSize=40;fontFamily=Helvetica;" vertex="1" parent="GSmwUHLEAzuctVDBRrmO-1">
          <mxGeometry x="2000" y="40" width="1370" height="520" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-66" value="TDA4 SoC Machine (QEMU process)" style="swimlane;whiteSpace=wrap;html=1;startSize=40;fontFamily=Helvetica;" vertex="1" parent="zDmAOu5_yZ2aFvSzQ19k-65">
          <mxGeometry x="20" y="60" width="727" height="340" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-67" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=1;entryDx=0;entryDy=0;fontFamily=Helvetica;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-66" source="noeFzWJ70rmPxRtoa0zJ-0" target="zDmAOu5_yZ2aFvSzQ19k-78">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="370" y="200" as="sourcePoint" />
            <mxPoint x="370" y="140" as="targetPoint" />
            <Array as="points">
              <mxPoint x="366" y="240" />
              <mxPoint x="366" y="240" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-68" value="read/write&lt;div&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(3.5)&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;fontSize=16;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-67">
          <mxGeometry x="-0.0143" y="1" relative="1" as="geometry">
            <mxPoint x="-2" y="12" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-69" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-66" source="noeFzWJ70rmPxRtoa0zJ-0" target="zDmAOu5_yZ2aFvSzQ19k-90">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-70" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;/font&gt;&lt;div&gt;&lt;span style=&quot;font-size: 16px; color: rgb(0, 255, 0);&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.4)&lt;/font&gt;&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 255, 0); font-size: 16px;&quot;&gt;(3.4)&lt;/span&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-69">
          <mxGeometry x="0.1157" relative="1" as="geometry">
            <mxPoint x="5" y="3" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-72" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.75;entryDx=0;entryDy=0;fontFamily=Helvetica;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-66" target="zDmAOu5_yZ2aFvSzQ19k-81">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="340" y="157" as="sourcePoint" />
            <mxPoint x="260" y="157" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-73" value="notify (IRQ)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-72">
          <mxGeometry x="0.0298" relative="1" as="geometry">
            <mxPoint x="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-74" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;entryX=0;entryY=0.75;entryDx=0;entryDy=0;exitX=1;exitY=0.75;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-66" source="zDmAOu5_yZ2aFvSzQ19k-78" target="zDmAOu5_yZ2aFvSzQ19k-86">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="460" y="157" as="sourcePoint" />
            <mxPoint x="540" y="157" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-75" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;notify (IRQ)&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px; color: rgb(0, 255, 0);&quot;&gt;(4.1)&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-74">
          <mxGeometry x="-0.3393" y="1" relative="1" as="geometry">
            <mxPoint x="15" y="14" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-76" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0.87;exitY=0.986;exitDx=0;exitDy=0;entryX=0.75;entryY=0;entryDx=0;entryDy=0;exitPerimeter=0;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-66" source="zDmAOu5_yZ2aFvSzQ19k-78" target="noeFzWJ70rmPxRtoa0zJ-0">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="429" y="140" as="sourcePoint" />
            <mxPoint x="426" y="300" as="targetPoint" />
            <Array as="points">
              <mxPoint x="440" y="177" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-77" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;notify (IRQ)&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px; color: rgb(255, 153, 51);&quot;&gt;(1.3)&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-76">
          <mxGeometry x="-0.119" relative="1" as="geometry">
            <mxPoint y="-12" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-78" value="Mailbox model&lt;div&gt;(QEMU device)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" vertex="1" parent="zDmAOu5_yZ2aFvSzQ19k-66">
          <mxGeometry x="336" y="97" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-79" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.25;exitDx=0;exitDy=0;fontFamily=Helvetica;entryX=0;entryY=0.25;entryDx=0;entryDy=0;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-66" source="zDmAOu5_yZ2aFvSzQ19k-81" target="zDmAOu5_yZ2aFvSzQ19k-78">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="280" y="117" as="sourcePoint" />
            <mxPoint x="340" y="117" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-80" value="read/write" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-79">
          <mxGeometry x="-0.6488" y="-1" relative="1" as="geometry">
            <mxPoint x="22" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-81" value="R5F&lt;div&gt;(QEMU device)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" vertex="1" parent="zDmAOu5_yZ2aFvSzQ19k-66">
          <mxGeometry x="164" y="97" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-82" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0;exitY=0.25;exitDx=0;exitDy=0;entryX=0.98;entryY=0.26;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-66" source="zDmAOu5_yZ2aFvSzQ19k-86" target="zDmAOu5_yZ2aFvSzQ19k-78">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="540" y="118" as="sourcePoint" />
            <mxPoint x="460" y="118" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-83" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.2)&lt;/font&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(4.2)&lt;/font&gt;&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-82">
          <mxGeometry x="0.256" y="-1" relative="1" as="geometry">
            <mxPoint x="15" y="-11" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-84" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;curved=1;exitX=0.25;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-66" source="zDmAOu5_yZ2aFvSzQ19k-86" target="zDmAOu5_yZ2aFvSzQ19k-88">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="590" y="97" />
              <mxPoint x="590" y="60" />
              <mxPoint x="95" y="60" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-85" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.1)&lt;/font&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(5)&lt;/font&gt; read/write&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-84">
          <mxGeometry x="0.1318" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-86" value="A72&lt;div&gt;(QEMU device)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" vertex="1" parent="zDmAOu5_yZ2aFvSzQ19k-66">
          <mxGeometry x="595" y="97" width="100" height="80" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-87" value="" style="group" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-66">
          <mxGeometry x="7" y="100" width="100" height="230" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-88" value="Memory" style="rounded=0;whiteSpace=wrap;html=1;align=center;labelPosition=center;verticalLabelPosition=top;verticalAlign=bottom;fontFamily=Helvetica;" vertex="1" parent="zDmAOu5_yZ2aFvSzQ19k-87">
          <mxGeometry width="100" height="230" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-89" value="" style="group;fontFamily=Helvetica;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-87">
          <mxGeometry y="80" width="100" height="120" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-90" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Share memory&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;(QEMU)&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" vertex="1" parent="zDmAOu5_yZ2aFvSzQ19k-89">
          <mxGeometry width="100" height="120" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-91" value="VRING-0" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" vertex="1" parent="zDmAOu5_yZ2aFvSzQ19k-89">
          <mxGeometry width="100" height="26.666666666666668" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-92" value="VRING-n" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" vertex="1" parent="zDmAOu5_yZ2aFvSzQ19k-89">
          <mxGeometry y="93.33333333333334" width="100" height="26.666666666666668" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-93" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1.01;entryY=0.302;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-66" source="zDmAOu5_yZ2aFvSzQ19k-81" target="zDmAOu5_yZ2aFvSzQ19k-88">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-94" value="read/write" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-93">
          <mxGeometry x="0.0347" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="noeFzWJ70rmPxRtoa0zJ-1" value="" style="group" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-66">
          <mxGeometry x="320" y="258" width="160" height="72" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-71" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;ivshmem-client&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;(QEMU device)&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" vertex="1" parent="noeFzWJ70rmPxRtoa0zJ-1">
          <mxGeometry y="22" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="noeFzWJ70rmPxRtoa0zJ-0" value="&lt;div&gt;PCI-Bar&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" vertex="1" parent="noeFzWJ70rmPxRtoa0zJ-1">
          <mxGeometry width="160" height="22" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-95" value="C7x Host emulation (Non QEMU process)" style="swimlane;whiteSpace=wrap;html=1;startSize=40;fontFamily=Helvetica;" vertex="1" parent="zDmAOu5_yZ2aFvSzQ19k-65">
          <mxGeometry x="1040" y="60" width="306" height="340" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-96" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-95" source="zDmAOu5_yZ2aFvSzQ19k-98" target="zDmAOu5_yZ2aFvSzQ19k-110">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="201" y="276" as="sourcePoint" />
            <mxPoint x="201" y="196" as="targetPoint" />
            <Array as="points">
              <mxPoint x="275" y="295" />
              <mxPoint x="275" y="160" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-97" value="&lt;div&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(2.1)&lt;/font&gt;&lt;/div&gt;notify" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;fontSize=16;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-96">
          <mxGeometry x="-0.0321" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-98" value="ivshmem-client&lt;div&gt;(thread)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" vertex="1" parent="zDmAOu5_yZ2aFvSzQ19k-95">
          <mxGeometry x="40" y="260" width="200" height="70" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-99" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;entryX=0.188;entryY=-0.014;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.143;exitY=0.975;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-95">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="50.01999999999998" y="178.9999999999999" as="sourcePoint" />
            <mxPoint x="50.07999999999993" y="259.01999999999987" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-100" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(153, 204, 255);&quot;&gt;&lt;b style=&quot;&quot;&gt;(2.3)&lt;/b&gt;&lt;/font&gt;&amp;nbsp;Init&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-99">
          <mxGeometry x="0.1" relative="1" as="geometry">
            <mxPoint y="6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-101" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-95">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="201" y="180" as="sourcePoint" />
            <mxPoint x="202.02040816326553" y="260" as="targetPoint" />
            <Array as="points">
              <mxPoint x="201" y="220" />
              <mxPoint x="202" y="220" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-102" value="&lt;div&gt;&lt;font style=&quot;font-size: 16px; color: rgb(255, 153, 51);&quot;&gt;(2.2)&lt;/font&gt;&lt;font style=&quot;font-size: 16px; color: rgb(0, 255, 0);&quot;&gt;(2.1)&lt;/font&gt;&lt;/div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;vring&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-101">
          <mxGeometry x="-0.1393" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-103" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0.363;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-95">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="121.59999999999991" y="180" as="sourcePoint" />
            <mxPoint x="129" y="260" as="targetPoint" />
            <Array as="points">
              <mxPoint x="129" y="180" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-104" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(3.1)&lt;/font&gt; kick&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-103">
          <mxGeometry x="-0.1583" y="1" relative="1" as="geometry">
            <mxPoint y="-6" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-105" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-95">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="135" y="100" as="sourcePoint" />
            <mxPoint x="135" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-106" value="&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(1)&lt;/font&gt; send&amp;nbsp;&lt;span style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;msg&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=16;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-105">
          <mxGeometry x="-0.08" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-107" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-95">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="230" y="100" as="sourcePoint" />
            <mxPoint x="230" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-108" value="&lt;font style=&quot;font-size: 16px; color: rgb(255, 153, 51);&quot;&gt;(4)&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;recv msg&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-107">
          <mxGeometry x="-0.3047" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-109" value="Application" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" vertex="1" parent="zDmAOu5_yZ2aFvSzQ19k-95">
          <mxGeometry x="40" y="60" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-110" value="IPC Driver" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" vertex="1" parent="zDmAOu5_yZ2aFvSzQ19k-95">
          <mxGeometry x="40" y="140" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-134" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0.099;exitY=0.999;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-95">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="49.80000000000064" y="99.96000000000004" as="sourcePoint" />
            <mxPoint x="50" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-135" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(153, 204, 255);&quot;&gt;&lt;b style=&quot;&quot;&gt;(2.2)&lt;/b&gt;&lt;/font&gt;&amp;nbsp;Init&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-134">
          <mxGeometry x="0.1" relative="1" as="geometry">
            <mxPoint y="-2" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-111" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-65" source="zDmAOu5_yZ2aFvSzQ19k-113" target="zDmAOu5_yZ2aFvSzQ19k-127">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="830" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-112" value="&lt;font style=&quot;font-size: 16px; color: rgb(153, 204, 255);&quot;&gt;&lt;b&gt;(1)&lt;/b&gt;&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;initialize&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-111">
          <mxGeometry x="-0.1061" y="-1" relative="1" as="geometry">
            <mxPoint x="-1" y="-34" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-113" value="ivshmem-server&lt;div&gt;process&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" vertex="1" parent="zDmAOu5_yZ2aFvSzQ19k-65">
          <mxGeometry x="780" y="410" width="240" height="80" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-114" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-65" source="zDmAOu5_yZ2aFvSzQ19k-98" target="zDmAOu5_yZ2aFvSzQ19k-113">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1180" y="450" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-115" value="&lt;font style=&quot;font-size: 16px; color: rgb(153, 204, 255);&quot;&gt;&lt;b&gt;(2.4)&lt;/b&gt;&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;connect&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-114">
          <mxGeometry x="0.4148" relative="1" as="geometry">
            <mxPoint x="26" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-116" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-65" source="zDmAOu5_yZ2aFvSzQ19k-71" target="zDmAOu5_yZ2aFvSzQ19k-113">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="440" y="450" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-117" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(153, 204, 255);&quot;&gt;&lt;b style=&quot;&quot;&gt;(2.2)&lt;/b&gt;&lt;/font&gt; connect&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-116">
          <mxGeometry x="0.5029" y="1" relative="1" as="geometry">
            <mxPoint x="-50" y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-118" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;startArrow=classic;startFill=1;fontFamily=Helvetica;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-65" source="zDmAOu5_yZ2aFvSzQ19k-71" target="zDmAOu5_yZ2aFvSzQ19k-98">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="650" y="355" />
              <mxPoint x="650" y="355" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-119" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;span style=&quot;color: rgb(0, 255, 0);&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.6)&lt;/font&gt;&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 255, 0);&quot;&gt;(3.2)&amp;nbsp;&lt;/span&gt;Notify &quot;VRING ID&quot;&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-118">
          <mxGeometry x="-0.1593" y="1" relative="1" as="geometry">
            <mxPoint x="-77" y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-120" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1.011;entryY=0.679;entryDx=0;entryDy=0;entryPerimeter=0;fontFamily=Helvetica;exitX=0;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-65" source="zDmAOu5_yZ2aFvSzQ19k-98" target="zDmAOu5_yZ2aFvSzQ19k-125">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-121" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(3)&lt;/font&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(2.2)&lt;/font&gt;&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-120">
          <mxGeometry x="0.0091" y="-1" relative="1" as="geometry">
            <mxPoint x="-19" y="-10" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-122" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;entryX=-0.021;entryY=0.55;entryDx=0;entryDy=0;entryPerimeter=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-65" source="noeFzWJ70rmPxRtoa0zJ-0" target="zDmAOu5_yZ2aFvSzQ19k-125">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="799" y="212" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-123" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;br&gt;&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;span style=&quot;background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); color: rgb(0, 255, 0);&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.5)&lt;/font&gt;&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 255, 0); background-color: light-dark(#ffffff, var(--ge-dark-color, #121212));&quot;&gt;(3.3)&lt;/span&gt;&amp;nbsp;&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-122">
          <mxGeometry x="0.0258" y="2" relative="1" as="geometry">
            <mxPoint x="-32" y="17" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-124" value="" style="group;fontFamily=Helvetica;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-65">
          <mxGeometry x="840" y="120" width="120" height="180" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-125" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Shared memory&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;(Host)&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" vertex="1" parent="zDmAOu5_yZ2aFvSzQ19k-124">
          <mxGeometry width="120" height="146.66666666666666" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-126" value="VRING-0" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" vertex="1" parent="zDmAOu5_yZ2aFvSzQ19k-124">
          <mxGeometry width="120" height="26.666666666666664" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-127" value="VRING-n" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" vertex="1" parent="zDmAOu5_yZ2aFvSzQ19k-124">
          <mxGeometry y="120" width="120" height="26.666666666666664" as="geometry" />
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-128" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="zDmAOu5_yZ2aFvSzQ19k-65" target="zDmAOu5_yZ2aFvSzQ19k-113">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="900" y="580" as="sourcePoint" />
            <mxPoint x="870" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-129" value="&lt;font style=&quot;font-size: 16px; color: rgb(153, 204, 255);&quot;&gt;&lt;b&gt;(1.1)&lt;/b&gt;&lt;/font&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;&amp;nbsp;start&lt;/span&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-128">
          <mxGeometry x="-0.1061" y="-1" relative="1" as="geometry">
            <mxPoint x="-6" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-130" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;" edge="1" parent="GSmwUHLEAzuctVDBRrmO-1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="2320" y="560" />
              <mxPoint x="2320" y="560" />
            </Array>
            <mxPoint x="2320" y="620" as="sourcePoint" />
            <mxPoint x="2320" y="440" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-131" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(153, 204, 255);&quot;&gt;&lt;b style=&quot;&quot;&gt;(2.1)&lt;/b&gt;&lt;/font&gt; start&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-130">
          <mxGeometry x="0.5029" y="1" relative="1" as="geometry">
            <mxPoint x="1" y="40" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-132" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;" edge="1" parent="GSmwUHLEAzuctVDBRrmO-1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="3280.04" y="560" />
              <mxPoint x="3280.04" y="560" />
            </Array>
            <mxPoint x="3280.04" y="620" as="sourcePoint" />
            <mxPoint x="3280.04" y="440" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zDmAOu5_yZ2aFvSzQ19k-133" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(153, 204, 255);&quot;&gt;&lt;b style=&quot;&quot;&gt;(2.1)&lt;/b&gt;&lt;/font&gt; start&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" vertex="1" connectable="0" parent="zDmAOu5_yZ2aFvSzQ19k-132">
          <mxGeometry x="0.5029" y="1" relative="1" as="geometry">
            <mxPoint x="1" y="40" as="offset" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="1pOd8WxGcqiPk_7VYDv-" name="Page-3">
    <mxGraphModel dx="1308" dy="617" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="1654" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-1" value="TDA4 SoC Machine (QEMU process)" style="swimlane;whiteSpace=wrap;html=1;startSize=40;fontFamily=Helvetica;" parent="1" vertex="1">
          <mxGeometry x="36" y="80" width="727" height="340" as="geometry" />
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=1;entryDx=0;entryDy=0;fontFamily=Helvetica;exitX=0.291;exitY=0.013;exitDx=0;exitDy=0;exitPerimeter=0;" parent="RkeaKlaHC3bexx9Da_NZ-1" source="RkeaKlaHC3bexx9Da_NZ-6" target="RkeaKlaHC3bexx9Da_NZ-13" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="370" y="200" as="sourcePoint" />
            <mxPoint x="370" y="140" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-3" value="read/write&lt;div&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(3.5)&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;fontSize=16;" parent="RkeaKlaHC3bexx9Da_NZ-2" vertex="1" connectable="0">
          <mxGeometry x="-0.0143" y="1" relative="1" as="geometry">
            <mxPoint x="2" y="18" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-4" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;" parent="RkeaKlaHC3bexx9Da_NZ-1" source="RkeaKlaHC3bexx9Da_NZ-6" target="RkeaKlaHC3bexx9Da_NZ-25" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-5" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;/font&gt;&lt;div&gt;&lt;span style=&quot;font-size: 16px; color: rgb(0, 255, 0);&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.4)&lt;/font&gt;&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 255, 0); font-size: 16px;&quot;&gt;(3.4)&lt;/span&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="RkeaKlaHC3bexx9Da_NZ-4" vertex="1" connectable="0">
          <mxGeometry x="0.1157" relative="1" as="geometry">
            <mxPoint x="5" y="3" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-6" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;ivshmem-client&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;(QEMU device)&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="RkeaKlaHC3bexx9Da_NZ-1" vertex="1">
          <mxGeometry x="320" y="260" width="160" height="70" as="geometry" />
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.75;entryDx=0;entryDy=0;fontFamily=Helvetica;" parent="RkeaKlaHC3bexx9Da_NZ-1" target="RkeaKlaHC3bexx9Da_NZ-16" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="340" y="157" as="sourcePoint" />
            <mxPoint x="260" y="157" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-8" value="notify (IRQ)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="RkeaKlaHC3bexx9Da_NZ-7" vertex="1" connectable="0">
          <mxGeometry x="0.0298" relative="1" as="geometry">
            <mxPoint x="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;entryX=0;entryY=0.75;entryDx=0;entryDy=0;exitX=1;exitY=0.75;exitDx=0;exitDy=0;exitPerimeter=0;" parent="RkeaKlaHC3bexx9Da_NZ-1" source="RkeaKlaHC3bexx9Da_NZ-13" target="RkeaKlaHC3bexx9Da_NZ-21" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="460" y="157" as="sourcePoint" />
            <mxPoint x="540" y="157" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-10" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;notify (IRQ)&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px; color: rgb(0, 255, 0);&quot;&gt;(4.1)&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="RkeaKlaHC3bexx9Da_NZ-9" vertex="1" connectable="0">
          <mxGeometry x="-0.3393" y="1" relative="1" as="geometry">
            <mxPoint x="15" y="14" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0.75;exitY=1;exitDx=0;exitDy=0;" parent="RkeaKlaHC3bexx9Da_NZ-1" source="RkeaKlaHC3bexx9Da_NZ-13" target="RkeaKlaHC3bexx9Da_NZ-6" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="429" y="140" as="sourcePoint" />
            <mxPoint x="429" y="200" as="targetPoint" />
            <Array as="points">
              <mxPoint x="426" y="239" />
              <mxPoint x="426" y="239" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-12" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;notify (IRQ)&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px; color: rgb(255, 153, 51);&quot;&gt;(1.3)&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="RkeaKlaHC3bexx9Da_NZ-11" vertex="1" connectable="0">
          <mxGeometry x="-0.119" relative="1" as="geometry">
            <mxPoint y="-15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-13" value="Mailbox model&lt;div&gt;(QEMU device)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="RkeaKlaHC3bexx9Da_NZ-1" vertex="1">
          <mxGeometry x="336" y="97" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.25;exitDx=0;exitDy=0;fontFamily=Helvetica;entryX=0;entryY=0.25;entryDx=0;entryDy=0;" parent="RkeaKlaHC3bexx9Da_NZ-1" source="RkeaKlaHC3bexx9Da_NZ-16" target="RkeaKlaHC3bexx9Da_NZ-13" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="280" y="117" as="sourcePoint" />
            <mxPoint x="340" y="117" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-15" value="read/write" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="RkeaKlaHC3bexx9Da_NZ-14" vertex="1" connectable="0">
          <mxGeometry x="-0.6488" y="-1" relative="1" as="geometry">
            <mxPoint x="22" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-16" value="R5F&lt;div&gt;(QEMU device)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="RkeaKlaHC3bexx9Da_NZ-1" vertex="1">
          <mxGeometry x="164" y="97" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontFamily=Helvetica;exitX=0;exitY=0.25;exitDx=0;exitDy=0;entryX=0.98;entryY=0.26;entryDx=0;entryDy=0;entryPerimeter=0;" parent="RkeaKlaHC3bexx9Da_NZ-1" source="RkeaKlaHC3bexx9Da_NZ-21" target="RkeaKlaHC3bexx9Da_NZ-13" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="540" y="118" as="sourcePoint" />
            <mxPoint x="460" y="118" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-18" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.2)&lt;/font&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(4.2)&lt;/font&gt;&lt;/font&gt;&lt;div&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;read/write&lt;/font&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;" parent="RkeaKlaHC3bexx9Da_NZ-17" vertex="1" connectable="0">
          <mxGeometry x="0.256" y="-1" relative="1" as="geometry">
            <mxPoint x="15" y="-11" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;curved=1;exitX=0.25;exitY=0;exitDx=0;exitDy=0;" parent="RkeaKlaHC3bexx9Da_NZ-1" source="RkeaKlaHC3bexx9Da_NZ-21" target="RkeaKlaHC3bexx9Da_NZ-23" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="590" y="97" />
              <mxPoint x="590" y="60" />
              <mxPoint x="95" y="60" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-20" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;font style=&quot;color: rgb(255, 153, 51);&quot;&gt;(1.1)&lt;/font&gt;&lt;font style=&quot;color: rgb(0, 255, 0);&quot;&gt;(5)&lt;/font&gt; read/write&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="RkeaKlaHC3bexx9Da_NZ-19" vertex="1" connectable="0">
          <mxGeometry x="0.1318" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-21" value="A72&lt;div&gt;(QEMU device)&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="RkeaKlaHC3bexx9Da_NZ-1" vertex="1">
          <mxGeometry x="595" y="97" width="100" height="80" as="geometry" />
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-22" value="" style="group" parent="RkeaKlaHC3bexx9Da_NZ-1" vertex="1" connectable="0">
          <mxGeometry x="7" y="100" width="100" height="230" as="geometry" />
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-23" value="Memory" style="rounded=0;whiteSpace=wrap;html=1;align=center;labelPosition=center;verticalLabelPosition=top;verticalAlign=bottom;fontFamily=Helvetica;" parent="RkeaKlaHC3bexx9Da_NZ-22" vertex="1">
          <mxGeometry width="100" height="230" as="geometry" />
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-24" value="" style="group;fontFamily=Helvetica;" parent="RkeaKlaHC3bexx9Da_NZ-22" vertex="1" connectable="0">
          <mxGeometry y="80" width="100" height="120" as="geometry" />
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-25" value="&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;Share memory&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;(QEMU)&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;/div&gt;&lt;div&gt;&lt;span style=&quot;background-color: transparent; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;...&lt;/span&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="RkeaKlaHC3bexx9Da_NZ-24" vertex="1">
          <mxGeometry width="100" height="120" as="geometry" />
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-26" value="VRING-0" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="RkeaKlaHC3bexx9Da_NZ-24" vertex="1">
          <mxGeometry width="100" height="26.666666666666668" as="geometry" />
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-27" value="VRING-n" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="RkeaKlaHC3bexx9Da_NZ-24" vertex="1">
          <mxGeometry y="93.33333333333334" width="100" height="26.666666666666668" as="geometry" />
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-28" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1.01;entryY=0.302;entryDx=0;entryDy=0;entryPerimeter=0;" parent="RkeaKlaHC3bexx9Da_NZ-1" source="RkeaKlaHC3bexx9Da_NZ-16" target="RkeaKlaHC3bexx9Da_NZ-23" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-29" value="read/write" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="RkeaKlaHC3bexx9Da_NZ-28" vertex="1" connectable="0">
          <mxGeometry x="0.0347" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-35" value="Notify" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="RkeaKlaHC3bexx9Da_NZ-1" vertex="1">
          <mxGeometry x="624" y="282" width="49" height="26" as="geometry" />
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="RkeaKlaHC3bexx9Da_NZ-30" target="RkeaKlaHC3bexx9Da_NZ-31" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="956" y="513" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="RkeaKlaHC3bexx9Da_NZ-30" target="RkeaKlaHC3bexx9Da_NZ-6" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="872" y="242" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-30" value="ivshmem-client" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="1" vertex="1">
          <mxGeometry x="849" y="341" width="200" height="70" as="geometry" />
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-31" value="ivshmem-server process" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Helvetica;" parent="1" vertex="1">
          <mxGeometry x="465" y="475" width="240" height="80" as="geometry" />
        </mxCell>
        <mxCell id="RkeaKlaHC3bexx9Da_NZ-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="RkeaKlaHC3bexx9Da_NZ-6" target="RkeaKlaHC3bexx9Da_NZ-31" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="436" y="515" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="JPUyd07KPkAlNmm5jv6M-1" value="" style="endArrow=none;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="424" y="828" as="sourcePoint" />
            <mxPoint x="907" y="826" as="targetPoint" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="tyTzyenE1IO4CY4CEGoD" name="Page-4">
    <mxGraphModel dx="1083" dy="539" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="1654" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-1" value="SoC View" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="720" y="40" width="160" height="320" as="geometry" />
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-7" value="Region 1" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="m3XBTr2kCk1S9m1vjy7l-1">
          <mxGeometry y="20" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-8" value="Region 2" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="m3XBTr2kCk1S9m1vjy7l-1">
          <mxGeometry y="60" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-9" value="Region n" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="m3XBTr2kCk1S9m1vjy7l-1">
          <mxGeometry y="280" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="bpUn21p71q_MDf-lG9of-3" value="Region 3" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="m3XBTr2kCk1S9m1vjy7l-1">
          <mxGeometry y="100" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="tkaZd_RN_GHhowruxK_n-1" value="Mailbox" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="m3XBTr2kCk1S9m1vjy7l-1">
          <mxGeometry y="200" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="tkaZd_RN_GHhowruxK_n-2" value="Vring" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="m3XBTr2kCk1S9m1vjy7l-1">
          <mxGeometry y="160" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-10" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="m3XBTr2kCk1S9m1vjy7l-4" target="m3XBTr2kCk1S9m1vjy7l-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bpUn21p71q_MDf-lG9of-1" value="Direct" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="m3XBTr2kCk1S9m1vjy7l-10">
          <mxGeometry x="-0.2233" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-11" value="" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,iVBORw0KGgoAAAANSUhEUgAAA5sAAAGSCAYAAABt+9XxAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAFiUAABYlAUlSJPAAAJbKSURBVHhe7f19cCTbXR/+vwcDSaCkEEhwmRic0dX+anssJYxMAlQAs2VjjMYS7MIsuKdGiSOqjGMSnGik61o218Ha3HA1PTw4fM3XSQmDxEwle9FukO5Mfct2SWVjnoqgibNCvSDdGcq+a1s8hNRNhVRCJf37ox/m9OnTDzPTI2m071dVV0l9unu6T/f0OZ8+p89kLMuyQERERERERJSiL5FnEBEREREREQ2LwSYRERERERGljsEmERERERERpY7BJhEREREREaWOwSYRERERERGljsEmERERERERpY7BJhEREREREaWOwSYRERERERGljsEmERERERERpY7BJhEREREREaWOwSYRERERERGljsEmERERERERpY7BJhE91VpLGWQy8VOu2pVXDehWc87yJbTkRNlpDTln26U9OXFQXdRmpX2frSF+z4O8fFlSH0nvWJPnj5KQD2GTctsR6/XyU5EfgUk+Vy2UAstEnaN+l3fslUI+X0X+DMU63vacacDzPt6YT0RElw2DTSKiK6GFUmYKlSNp9lEFU6pKd4RuNYfCtjy3p7WUwdSa6Ztnrk2pg8IUmGtTwaDXPIR/D1KwV0ImU0BDng+gsagIevtd3nVaQ25RtVZSDdSlYLb14jDbu6qYT0REF43BJhHRFdCtVpygR4NxYsGyLFgnBjQAQAOVsMDHx24JlANJn9MaKk4gqu/an9PZsD/FXCugdupfPDXbFd+2vaCh3LSPVZjqC73lkuuidic6EPEfX7/L27rVHDLXKkMHyu3H4vnswmwL/5KH+UREdLEYbBLRU21+SwxUOjBmnAQpiDleNRXdJaNaDKUufXLLXAS5a29oC5mniwdbTvgys4xb087s6VtYdo7HfBQX3oS0jEq6DzedQElHyQnqsqsGdACAicO4j4ngBq++adfesn/bvaBBm7UD3ViKoNSe6pgH7O27xy4vK+zD5kP3XPS7vLpFuF/ajBPYbz3odf88fYDNIwDQoZeFhUVx3Uelbt2+a9BdVuq6rL4uFV2X5Wvf91ni92QO3yDsQ/g6UprCoPkkf/cCn+fbD/lYo+4HRERPJwabRESxWigpu0s2UFBWMBsoyMtvF4IV/AC78ip3YVV2I/XJYuWRE+g8WkHWmy8ERIlpME6EoFviBa0zc06rKQBomHOW97ckjUrvuMy1qV5lPzZ/E5JaUbFQ9wLJ49Ve7nr6XF7b6HitwX1bWrYD+6NNPHA+03sAMDOHOf/SgNuaKnfbjehe3ViUrsGjCqZmc8hJLbLm2pQiEFM8sNguhAZijUXhe1K+h//XCQIbL/qXVj3kiDRAPrWWgt89OPmhCnAbi/KxNlDI5AKt2URETzMGm0REMbwuqjMGOoHWq+B7YYB/WS+wOKqgoGwNsnWrBafyKnSFdT9HDmgS6HWtBfSi3X4XacZAxzrGitsyOoD4FtRwjcVgq1IvSBKCjFMTyt6QEQGUHfDI2xcDg3nc8wJAE5VrveXULXj9Lg/vvKoC0OTcwL7X0uvmubZ0S3gA4DitoeC0pmobHScIbjot0WHdq3U0Lcvf0n9kwnRbcL3u2eLDhS5qN91g1F3f35W7oHxgIiy7NY/5onu914XzKLTcl0tOS3Sc/vPJ7h4ufPe8fAoGvzZVl3UTlQ+qliUiejox2CQiipFdPRZaDZ2uc3JLkY8G42GvhTG72vQq7eHBmFihNnoB38I9Z11/l8w43Wqu12VzxsC92NagedR9raKXi77rdnf1t3J5gYoQ/KsDqHjZ1WM0Fd0rvdZTKVjqd/n5reECeZuGW0tO+PZiC0ALdac1Ln89ePbEvDK8ILcXKPu6mTq0jXtOXme9zwI0GM85Z0DVPdvrouo/V5heQdMNyn0BpK33WY6FkhcIew9xxG0neWgC9J1PmF7BsWXBch622KMtq3oz9Ggbzd75jDlOIqKnFYNNIqJY4rtZctc5lTw0X1CRhZZ3/mybgcq9Tejy6muF631eeKDq5ws0oaMpBJHyT5Zk5HfSLpVey5E46I8X/HvvW9pdV93ATxVAJeW+w6vs5rpdCLRa9rt8GrLXnYtpu47WXt0JiNTdS3vXTAMF4Zx718dRcFRfZTAWuKYl3ujAGuakrPD2F22YUut88LPmUZK60va6vyZ5aNLTTz5B+m4kebdW3veo4yQielox2CQiitFaEgJMt3us15J2uQQCTTEgOweJB+xR8AYIErskXkvpHTh5EB+hFUulF9D6u1OGBbL9Lj8UofWvcsfpUJy4e+nl5+9K22vx15Zu9dfy3k8+7ZWE7437kKN3HomIaDAMNomIIgkjn250pAF4wsgtG8JPLuS1kPV7g+yoAyP7nbYo5xFoesGkr0Ws1yort/YMZHoFx76AM+Q9zBSJrVrBll7h3DjH3e/y6ept3zxyArGQIL83X+hy7JtSukY0d8Co4IjE3cfexR/dOuryuo43UFm663Sh1bB8s99rK3k+efvYx3vL8mBYfR8nEdFTgMEmEVFCvVaquN9YNFG52RsZtTfwT9Q7Z8L7ceJgQEl/8kEYCCYq0PS3wNlTP79Lmb257A344r5TJ/7Gp9eFMul+h5lewbHwHqY4uEzv5ymEIHSv5I0kqj+f5IGAX++4VAMVCS3bTstYv8unS3yXEpGBmHi+eu+yCt3ClYP2DEB4j7OxKJwX8bpMnBe94zO3G04XWuEnfRJLnk8ecfRaYYAtFd/vqA50nEREVx+DTSKiSEKF9aiCKTmYCOMtK7z/FfPOmfh7ld7opu7PTcSs2/qg+LMU/vfz0g0qVmC479Q5QZZ7fL4BU9IgvIeJ7YIXtM4/J4xw6h6fO2BTTD6FEgd4CSUOktPn8ikTg93IljThfPV+Jsa9ftPcvyxWHirOi3D9dmJa5kX+4xugC60jaT71lut99+Lf21R8T1PNUyKi8cdgk4goRnDUUR1N4Wchgj+LoKMpv+9VbibogjuPumUFRziNXbc30uZ5mN8KDoijbXSG/EkPtfkt4ecn3Baz6RUcy/mLJPkUzW71VWwXbtdmf/fKfpdP1bQGdziauJa0+S3VO8Y6mmnv3/QKjsWfS3ENcl6EltJELZJhkuaTryXdpu8K17lihFl9V/6ujiBPiYjGXMayLEueSURERHRxuqjNOi2wMwY6/Qaro3JaQ85pxdR3++uCTkT0NGLLJhEREV0ue3e9ruqDdqElIqKLx2CTiIiILoe9kv8dXOgwRtA9m4iIzgeDTSIiIrqENBgn6lGViYhoPPCdTSIiIiIiIkpd4mDzl3/5l/Ef/sN/wGte8xo5iYiIiMbAK6+8gq/92q/Fl3/5l8tJRAP58z//c/zhH/4hcrmcnEQ0sCdPnmBychITExNyEl2A1772tfjpn/5pfMVXfIWcFCtxsGkYBn79138db3nLW+QkIiIiGgMf+MAH8I//8T/Ga1/7WjmJaCAnJyd48OABnn32WTmJaGC/8Au/gHw+j3ze+/EiukDvf//78YUvfGGg4L+vYPPx48dYXV2Vk4iIiGgMFAoFfOhDH8IzzzwjJxEN5Ld/+7fx4Q9/GL/0S78kJxEN7H3vex/e/va34+1vf7ucRBfgm77pm/D5z39+oGCTAwQRERERERFR6hhsEhERERERUeoYbBIREREREVHqGGwSERERERFR6hhsEhERERERUeqGDDY/icr167iunCr4pLz4kD75rL3tyoGckqLPfhSF69dx/XoBH/2smOAea/rHRUREdNW5Zbg7BcvyT6KSUhn7ymYBhc1X5NkBr2wWnP2Ry/wwr+CjC3F1Aae+sPBRxO8BDc2rt6mn4HXWh4MKrl+/nuha6l3fUdeGKMF10sfn0/mJvpcxXpANGWxGeQnvjvoCDeDNLzzG48ePYdyQU0bhZbzw3nT3n4iI6Gn0ymYB7/5V/7yX3iNW0j6JyvV34yX/IgN5ZbOAt1ZflmcrvIKP/0d3uZfxwv/DqiEN6pPY867vl7DB4PDKir+XvRnG48d4/NjAm/2LPbVSCjbfgY88tgNBe/oEnr0G4ORX8HHhSaH/SUAw4u+lF/DRzwaf+ChbNp2nPu7kf/rTe7rwUe/p5XVcf1b+5BAnL+DdkTeMYMtub9/cp58FfHSzt4/2/rlpqnyQtpl0X4mIiC4lN6h7Bs9+zK4nfGLV/p3Pl/6/T0qB5kt4t1CW9loenUl8iO22aC1UUHHK1Le9+21eoPly9a0xrUY/gxdOAHzvO/AOAPjVvUC9BNI+FDbVQaxYvwm2pPXqIhV3Obdsl1rl5BaswPFLdYK49KfKtWfxCV9d1N9A4dUhN8U8j6qDyWnhXtncwEsAnvned+AZAC//x48rr7vo6wTxn6+45t1rxn8tyC31Yr1T9flx6WSLu5ch2LIpxSnu1PuuX/16f0rBZrxPPntdehLgb/n0Pyl4GS+8dwNmb2G1gwquv8f/HPTl6lsVJ+olvCA+5fzVdyf+Er1c/Znglx1wvpjBp7AvvUe+ObyMF6q9pV6uvhuFhbfaBRxg54O3v4onu7/67kDhQ0REND5ej6kcfD2GXr/ctIOBFyKe/R9Ugi2UqofAJy/hpRMAeAbfc+3L/GkRPvn/2aXtO95uYOF7AeAl7Ml1A2kfXq6+Wyi/bXJLx0vvCdYNbC/hJWe5d7z9zXbg8LYXIB7hy9W39uonquMX6wRx6aT0UlXMc7EOJtfrXsK7pTqmWi8A+YH3vg8/oGhsQaLrpI/PF675H/iu1yta81/GC2/r1Uc/+axY77S99J5eQBqXTq4B72Whno56f0rBpv0kshexOxft967hXd8AeN0LhKdOn1h9Bjh5AT9zYKf/jPMlecfPO+nfB98NOOgVfPSnnILCWefx4484Tyc3Al8Qd5mPfK/9v9mJOZHXnsVHVp+RbkSi1+Nde4rWXJjoSJ/9zOonhM9+GS/DyYeff4e9wHHHDroP9uwnY87y7jbDA14iIqLL781vd8q7kxfw1sCT/TfDcMtvp6eUcQPADcPfSuWUmS//gVw7cFsZmvhnlabX0vDM6ifweO9deL20tM3t9vgOLNzo7V+vdQK+eoZXLn/sWdhbdwXrL17ZruAuY9wAXvn4r+BlRR3mpZ/yt8b26gT21Fz2H1Fc+lNDuLZ6k9wAAOB7P+I/T14dzGnpFnrruXXGSJ/9OH7lBMC1H8B3fcPr8V3f9wyAl/ErHxfPYoLrpK/P713z7/oGN9gVehn+/DsQ7M7ba42zp6ZTR0+aToi9lykI9zH33gS8A2vLr39q6v0pBZtB7/h5Icr/bMdupRROjPsExuy80ku/9ize53R3eP3ymlPwhPC+3L11gDfjfav2l/z3fGWRXZhAvEgSeMbdh5iWULtbRPCpkM1+6gQAz/z/nALw+77LLvyeeaOv0HqlY7flvlx9qz9oVwSwREREY+OGEQjU1D2RFNyug2GtPNd+AN/Vb6XYqeThexfs96puvM9+YOzrSvsyfs+p/K+5Adw3fJfdcuVS1F9wYyGk/tKri0AIml96jxsYOS0cJ79nP2x3ttOrE0hdG+PSSekdb3fqptJ5cutgz6y+z3vXLkmd0X1o4Nbt3Pqrryttguukr8/3XfPudSo0/DjfFfcas7fzMl54mzoIj0snwYD3sl7r8zN49mP2+5xPS70/pWDTfZriPpmUnw6GCz6hTOgbpqDJ81L3ZhjOk6eXfkru1tt7J7Pz3t7TiNGQg2ciIqIx8w3vQlN+wh/yniQgvOv0/0zZ68ktQUNwu9DiV98tVfIUXWnPnVvRdAYaESq2L71HfKcrLv0po3xnc9SDtPQGmeoFDO5Dg2BX2nPntto6rWu9ljX/u9Gx6eQ3wL3MbWR7x88naTG+WvX+lIJN15thuDc9sTXQDQxVN4IX3txL97rV9l62DvcM3njNv06vm8IzeKO/n8vgbhh2N4aTl/3den3dJiA8WRrc66fs8FnuEvP43EbgJSIiSlvwpwBiey/53qm0wwW3FWBon/0oNqTRJEW9bqxOPUPsjuh1dXQMVH+xub2det1o3UmqjHoVW/dVIalSG5dOibh1sF4Xxl436lDy9eAjjHCc4DoZ6PMB4TqVB+t8HOhG7r1f6DakSA1Dcek0wL3ssx9FwWlpfmb1E776/NNS70852LRveva7juJgOW+2X74P9Kd3Xz52u7/2upO89T9Cei9C9nq86587Xwa5C4r3rmg63vxCr8U2wDsm+UXvASi6xFy/HvMbTERERJeaUwfwje/gltdON1aPvYz4DpRXL5AHw4lhl6XB7oBut0fv3T1vcsp6r0WqV8/wyuVAV15F/SXhfr7+u34Az/jqMM7ktEwGRpqV8iwu/akTqGPaU+T7dCK3K7V3nYa9HtXjPhAJBAtew4sb+Ce4Tgb4fJv7nqg8forbMhkcada9ju0HOXHp1NPPvczm3W/k+v2zn3xq6v3pB5sAXr/8kd4XxrlpvvmF4IvOYlPy65ebQvo78JG9tfhusjeMQLeaZ1Y/MeCIUFF6NwmPEFQDTqH18+9A8KXwfoiDJLjegY+EDnBARER0+anqAPjejwjldbCc9T/ofQbPfsx5XSWm5S66paHX7TFYkXYrkkI5fsMQuhYC+N5nA6/M+OsvwDOrz0Z8vuAb3oWmPODQtWfxCSdPXr/cG+xokHTq1+vxrj3pmluNOpPuIFO9sTk83ru9vW7Z8ddJv5/fo7oWeq1or8e79oKveiVPJ1H8vawfT0e9P2NZliXPVDEMuz/36uqqnJSCV/BR5+dAnln9hD2Smjsk+LVn8YkrlulEREQXoVAo4EMf+hCeeSa67xBRUr/927+ND3/4w/ilX/olOYloYO973/vw9re/HW9/+9vlJLoA3/RN34TPf/7zmJiYkJNijaRls3/u79YITcnOb095I7cSERERERHR2LgkwSbw5heCTfj43o88vb8XRURERERENMYuTbBp9xmXRtEaqP8zERERERERXbRLFGwSERERERHRVcFgk4iIiIiIiFLHYJOIiIiIiIhSl/inT/7JP/kn+PjHP46/9/f+npxEREREY2BnZwdvectb8FVf9VVyEtFAvvCFL+A3fuM38P3f//1yEtHAPvGJT+Bv/s2/CU3T5CS6AP/+3/97/Mmf/AkmJyflpFiJg827d+/iN3/zN/F93/d9chIRERGNgR//8R/Hj/zIj+Drv/7r5SSigRwdHeHFF1/ET/zET8hJRAP76Z/+abzxjW/E2972NjmJLsDq6ir++I//eKDf2UwcbBqGgbOzM1SrVTmJiIiIxkAul8POzg5bCyg1BwcHWF9fx/7+vpxENLDbt2+jWCyiWCzKSXQBJicn8eTJk4GCTb6zSURERERERKljsElERERERESpY7BJREREREREqWOwSURERERERKljsElERERERESpSy/Y3Cshk8kgV+325p3WkMtkkAmbllriFtCt5vzpszUIW/O2V9oTZwri0omIiOictVASynZfPSFWF7XZ8HoD0+PSryZ/fbGEvo7aqa/aUw6103FJl861OLnn3beuf3K/d4G6tjDZ9eeIz+k3r8eaPx+C9y3/fS36u2dvKxifuNtw81XapjcprpMxklKw2UXtTgN6WYe5drd3IU6v4NiyYFkWLKsDYwbQd93/LVhb8731ZzOY2lpGx1veQjNfwVTgwtbQvqPK9BZK1yow5dlERER0QbqozRbQ3ug4ZXsT+bUpRaVLrbU0hUq+2atHtAu+Sh/To9OvpL0SptbyaDp1xc5GGwW5cSLMaQ25xTaME6euuZtH5ZpQz7zU6VmsPHLmu9OuDkCD8ZxbnwYwY/jq0u50vJrtLVN2rxn/VF/oLeKrr3tTHcInXWFd1GbF75Z832qhlBHva4N89+xtNMpNKV+13vl3ps4G/NfJuLESqlarVqVSkWfbTgxLg241raalA5a+Ky9gWZbVsYwZdVpnQ7MwY1gdOcFZR9twUk4MS4NmGRt6b55rV7dQ1iM+n4iI6OmmaZp1fHwszx6dXd0CdKspzOpsaBbK9pxmGVK6XY/QNjq9Mv9EWHlX79UXmB6dfk729/etGzduyLNHRKoXWpZzzTj5cGJYmnv9uGtsaN411izDu/acVF/d9LKn+wnfFVeC8y9+/9SiPvP8FItF6/79+/Ls86H4bvnOjeK+Fp33cp7a5y64vHAti7w46+JMTExYr776qjw7kVRaNrsPN2GWS5jHPEploPFiP7F3Fw+2TGhLtyA8c3HYT3F8T2MA4GYJ+a0HvqdYrRcb0IslYQ4RERFdpO7jNjAzB02Yl72eB7braAGY3+rAmGmg4rQItJbsJ/3Hq1nAPISJPLRpYWVtDtrRJh6cgulx6VeSicMjIH9drBdqmJsxsfmwa/eo2xV62Z3WUFgDjJM65tGF2Qa0Wd/VCC3v1lsve7pft1pBY8ZAU64j0/CmV3BsHWPF+261UN8G9KLT/rhQD7Tydh+3hf+iOC2aMwY6j1YUsU9Q9+EmsHFvbFuVUwg2W7i7ZnonYP45A9p2RdHNNYzqxhFnHqW8eDNtob6toyQ0/xMREdElkNf8FSpNDD6zWHleh7lWQK1aQmFbR9N7xQaBQBXTGvLi/0yPTr+SNMz5DtoOyDwL92DMNFBYqqF2swJsNIWgIVjf9Ad3lz/d5tS9n1cEK0cVTMnv/MnvE24XAu8Fyl1AG4tX673BwbjvbdoPwcRuxn4R58PHfq2gAUQsa6JyzZ/3U2smzEfj+6Lg8MHmXh0NCIHe9C0su0+YRmi+mO99xl4djXJpbCN+IiKip9ZCHc2yicpaA/ru0/JOGI1OFisP7YaPCq5oy59c9xap3tkUH+BA/c6m3Isw+M6m2NL3tOi9J9uZrQQHLgV8716GB6O2xuIUKjBglIHGYtg7mMF3Nq1dHeirIe9yGTLYtAcGAhooeBH4FCpH8A8UFEnD3AzQfhw8fZEWSsg7n2F3oWXxREREdOm0TX8FzTwMHcwvUBc4kpY9NeHrrMb06PQrycSh76Dt7qdKcv4orjG5xeiyp8Op94KNLOcqe3NZ0UVdGORHDuhVnK6zK87rA4kHtlq4B+McGvJGZbhg8/QBNo8UEfiJAQ0N1BONNpfFrSUNpvQOpqu1pGj+B+yutOUG6nvsQktERHQZZa/nAxX+7uO2v6K853Sf3XW607qVOW0OGtowxcqdeQhzZhm3ppkem34lqRooTBweaVi+6bbMdb3us3Z3WrcOaXe39QdvdqBqN1hc9nSX9P4gpW+vFP8zL6c15NwRaZMEmr6us07r+1EFhT5GsJW7WI8NecSgMKrRaMNHke13RC07Td6WPUqdMCqTPDrUrm4B4udEjYZLRET0dDv30WgDo4fK5bT//2bZXxfw1yXkbTE9Lv08nO9otMGRQOW6qO9/Z3Ra73pT1iOFUT4ve7pqGVHkiKg2jkabhPxdcuIUKd5I/l0LyVMnjvHfDxXnVnUdnLNhRqMdItiMyehd3R8ohmW0wA4uhUn+wgS+YNGFFhEREfWcf7BpeWWzW7YHKnC+sl6uWzjLuOsHKslMj04fvXMPNi3350zc4xYq4YHKu/+nT8Rl7CmsYn/Z00MCDwabKfLft8Q8819/4hRyXiLy1I197DTpM6Oug3M2TLCZsSzLkls7VQzDwNnZGarVqpxEREREYyCXy2FnZweaphrhkqh/BwcHWF9fx/7+vpxENLDbt2+jWCyiWCzKSXQBJicn8eTJE0xMTMhJsYZ7Z5OIiIiIiIhIgcEmERERERERpY7BJhEREREREaWOwSYRERERERGljsEmERERERERpY7BJhEREREREaUu8U+f/PiP/zg+/elPY35+Xk4iIiKiMfD888/jh3/4h/G1X/u1chLRQF5++WU8ePAAq6urchLRwDY3N3H9+nX8/b//9+UkugAf+MAH8Md//McD/fRJ4mBzZWUFn/rUp/CWt7xFTiIiIqIx8G/+zb/BD/zAD+B1r3udnEQ0kD/4gz/Axz72Mfzoj/6onEQ0sO3tbUxNTTHYvCQ+9KEP4Ytf/CImJyflpFiJg03DMHB2doZqtSonERER0RjI5XLY2dmBpmlyEtFADg4OsL6+jv39fTmJaGC3b99GsVhEsViUk+gCTE5O4smTJwO1bPKdTSIiIiIiIkodg00iIiIiIiJKHYNNIiIiIiIiSh2DTSIiIiIiIkodg00iIiIiIiJK3ZDBZhe12QwyGcU0W0NXXhwA0EJJkd5aUmzDm3KonQoLh3K2rZqWWsJyEfudyaC0JywaunwJ4hbdZXJV+agV6/r2RVhGlWd7peBn7ZWE7SXNGyIioovgL5uD5WQUqQwNKz8vbbpULwmkx60/bPpVM9zxdqu53rpy3SqFdFe3mgvsm39d/2TXOxX1xcBnRdRzvTpkgmV89Uj/5H4/4/f3KvOfi+A9K+57LbKXDW7DJaeHXwfjmu9DBps2fdeCZYlTBwYqmFJkfrdaQaOsQz+q4K6QafNbwvq7OjBjoONt7xgr0+JWomgwTuT9aULfLgROdHC/7am+ICx0WkMuM4XNpY5vmc5GG4W4QM9Zt5Jv+tZtoqC+SR1VUAi9GB2nNeQW271j3M2jck2xLSIiogvXRW22gPaGW4Y2kV+bSlxpai2JZWgHRttfll/udPvYG+Ww9Lj1h0+/aoY63r0SptbyaIr1OPEh/7Dpjm41h6k1U5rr8K4F/yTWO9V10zrmvSVU9VwL1qMVZPtZxlfP7k3Hq72tJNnfq6eL2qx4ncn3rBZKGfGelvA63Coo7nvOPUKerboOdnU0FmPijsvKSqharVqVSkWa27GMGVj6rjTbsixrV7cA3Wr6ZvaWb5ZhoexP9ezqFmYMqyPPj9W0dGiWcSLPt6zOhiZsM2K/fezltA31njTLYpq8rPy/n//4nf0p6xbk/ZfyMZhvSY+FiIiedpqmWcfHx/Ls0VHUBTobmleONcuQ0puWDqfsPDEsTVUmumX5ZU93jkUsn8Vjj11/2PRzsr+/b924cUOenb6Y4+1saFIdyq4f2fmtqpOJdcZh0939s69nPVBXk869UpL6XHg9tyfBMgmuk/j9Ha1isWjdv39fnj16iuvMV/dW3NOi89O5D2wYlibn54lhaTO6pSvih+B1kOC8jtDExIT16quvyrMTSaVlM7HTB9g80lFaAOaLOrBdv9wtcs7+GuJTHsH8lvQESHT6AJtHGpZvqtPnnzOgycdfrKNZNlG5GXxSZuvCbAParPhj3FloeaDx4qXOSSIiegp1H7eBmTn4Sq3rea/8n9/qwJhpoOK0CrSW7JbA49UsYB7CRB6a2LNJm4N2tIkHp7j86dAwNyOWz1082DJ7ZXjc+sOmXzUxx5tdPbbrUB+087tbLaACA52teQAmDo+A/HWxTqZhbsbE5sNuCum25V0LllVHSViKxsz0Co59PSpbqG8DetFpW16oSy3Nzn0uzvVbWG776/3dh5vAUglzwrxQe3U0ykYfPT0vjxEFm13U7jSAcsl3MlofrMB05y3c8xUwo9XC3TUT2tItoYsB0FgM9of2dW81D2FKhWRiqpuiaFpDHm2YUoEwv9WEHtOd1n+zk4NPIiKiSySv+cpeaGK5msXK8zrMtQJq1RIK2zqaW0LNQS6DpzXkxf8vdXoWK4/cV2cyyGSmcPi89JA6cv0U0q+amOO1H+QXUKrWUFgDjIf+rqVzvpXth/U9Q6ZPr2AlrnvptnsthL8PGKybyl0nTVSuycvIr1MlWOaogilpXwLvHibY36vLfXfSfgAW3nXYjjH058VrTSWLW0tt1L2utF082AKWb6rr8IHrYLEBtM2QxqjLLZVgM5Ah7nuKYoEhPxlAFreWNJhbD1LOONUXzO5bLbdCBvpDW/YTKXGvZYEXphX99Yczj/quU/BexSeTREREogWnV89aA/pudBk8XuyBPyqzvXe75u4oKvSUnukVNDc0NNYqwEbz8rUCKd6BjK+byuOWqN7HlL83CZZRvbPpq7cn29+ry35YZFkWOrOVkPq+/f5mdDDak725jLbb0+H0ATaxjFsh12jgOjgxoEnj3YyLVIJNMUM6G5p9kT/nv2C71QoaUmA6tWYCqWec/wvWLANAeFfYSNoctKNDiK95Z1ePpWMNoc1BU7Rcek5NtMNaPt2CN6Q7bfuxf675KORFdCIioosmP403/eWqSC7fIJXBdtkpuMzpe3U0fPWPLFYeGtC2K72HyVHrI4X0q6aP4w3WjUwc+mbZryb1DJtOV1X25rKie3ov0AwE6WGmb2G5bX//7S60/h6XkaZXYJTH87W5VIJNkddn3jdCqvOegjdykzt1YPjeZ0if+z6IasSwWNO3sDxoV9/pW1iW+vKLfF2KFbzutHfEO5ndZcN/A7Vvdr0WYyIiosshez0fCBC6j9v+12z2nO6zcq8e1UNb8xDmjNMacNnT48StP2z6VZPkeE+d7rO7TndarzHDfn/W/zDDxKE3tsaw6XRlqH5yUHZaQ84dkTZpoAk4vTqBzYctpwtt/9fOWL46J48YFKa/0WjtkZciR0xz9T2qU5SQkZrcEcLk0V8D+63grBsYVXZXtyAeo2qkssDn2oKj74Xsj/sZ4rJyXqryj4iISOHcR6MNlI3yCK3+/5tlsVyVR2CXt3XZ04WRdYV0sU4Qvf7w6efh3EajjT1e///26LRC/UiqL/l/pSCFdEHwlwOSjO4aUhf0Cann+iRYJkE9O35/R+vCRqMNua56eSF/r+NI9zw3NgiNH0KuA7n+f86GGY12RMGmP9CSCw8/xUlL8CVQi/iCOYGb/TnOhYOQKfDlUi0vf458sfjnJ9m+Kh+DgakYhKr2g4iISO38g03LK+fdckuuVPnLe7lOIJWhIeXn5U33H3uwbhO3/rDpo3eewWbU8QbrS86ywjJ2AOquH3xQP2y6i8Hm8C4u2LSC39vQa0icwq4H+QGbHC+o/w9uP+7aGK1hgs2MZVmW3NqpYhgGzs7OUK1W5SQiIiIaA7lcDjs7O9C0MeyKRZfSwcEB1tfXsb+/LycRDez27dsoFosoFotyEl2AyclJPHnyBBMTE3JSrNTf2SQiIiIiIiIan2BzryT9nIk0DTIAEBEREREREY3E+ASbC3VpJFtpehT3Y6pERERERER0XsYn2CQiIiIiIqKxwWCTiIiIiIiIUsdgk4iIiIiIiFKX+KdPPvCBD+B3f/d38c53vlNOIiIiojHwT//pP8X73/9+fN3XfZ2cRDSQ4+Nj/NIv/RJeeOEFOYloYC+88AK+8Ru/Ed/93d8tJ9EF+OEf/mH80R/90UA/fZI42FxeXsYnP/lJfMu3fIucRERERGPg4cOH+I7v+A58zdd8jZxENJDPfe5z+J3f+R3cunVLTiIa2Mc//nG87nWvw8zMjJxEF+Cll17C5z73udEGm4Zh4OzsDNVqVU4iIiKiMZDL5bCzswNN0+QkooEcHBxgfX0d+/v7chLRwG7fvo1isYhisSgn0QWYnJzEkydPBgo2+c4mERERERERpY7BJhEREREREaWOwSYRERERERGljsEmERERERERpY7BJhEREREREaVuyGCzi9psBpnZGrpy0l4JmUwJLWl2t5pDJpMRpuAy9roZlPbkBPvzctXAp4Vw9s/3ec6k2mcAQAslRXprSbENb8qhdiosHEmxT0uBHACS5pXLybNEyyop9sudpLwI7lf0sQTzTs6v8PMa/KzgcdnLyNuEdy7915Fzfp1J9ZlERJSmYe67UtkUKGOGTffvWzA9bv3Lnn7VDHe8/jpFWH0ipfQ+6k52PSWiHuZ9lnS9ipP3eQmWCdQZe5P7/Yzf36vMfy6C96y4+4bIXja4DZecHn4djGu+DxlsOo4qKIRmosvOvKm1PJqWBcuddoFCSAY2FoNf5EHou8LnWRYsqwMDFUwpLo5utYJGWYd+VMFdYZ/mt8R91oEZAx1ve8dYmRa3EqaL2uwUNpc6/n1pF6SbUn951VrKILMI37KdjTYKygAsWuK8Kjel5Zxpa15YyP4CFSAtu5tH5VrwOPz6ywPAROVm2AMEVxe12QLaG27+N5Ffm1Jsi4iI0jHcfbe1NIVK3i1D7PJSrLQNl95CKSPum5wet/7lT79qhjrevZKvTtHZaKMg1r2GTO9Wc770Zj553am+0FskWA+zYFl19GpXGowTOd2C9WgF2X6W8dVje9Pxam8rSfb36rHr6r3rTL5nxd83lLYKivuefX9syLNV18GujsZi//X6S8FKqFqtWpVKRZrbsYwZWHpZtwDNMk6EpF3dAnSr6S65oVmYMayOsIhHWtba1S3M6JY+Awtlb673edqGcisKzv7tyvMVn2lZvuWbZfmzBbt6+LFEOTEsTc4nxfy+8ypwHLbIYwhInledDS3RdqM+37+N4HntJw/sbemWDvnaaFo6hGNS5FXSYyEiugo0TbOOj4/l2aMTc99tliGl2/dtbaMTKBstSyp/h01X7Ftf61/29HOyv79v3bhxQ56dvpjj7WxoUl3UrlvY11qwnmFfa+7yI0hX1lWi6hsR9TCP+JlhEiyT4DqJ39/RKhaL1v379+XZo6e4znz12bj7RoBTF90wLE3OzxPD0px4p3fthF0HCc7rCE1MTFivvvqqPDuRdFo2i3U0y1EtS1082DKhLd0SnroIFu7BmGmg7ov453DvoQFtW/UkYEROH2DzSEdpAZgv6sB2PZWWVc+0hjxMVD4obXV6Bcde62h/edV6sQGUS8ITr575Lbml8Ty1UN/WYDyn/vzs6nHEvvWXB7YS6rs6zLVC6FOf7uM2MDMH8afMs9fz6Z9nIiICEtx357c6MGYaqDitAq2lAhrlpt26Yh7CRB6a2HNIm4N2tIkHpxg+faEutRg5++uKW/+yp181McebXT2266JOHatbLaACA52teQAmDo+A/HWxVqFhbsbE5sNuCulZrDzytwpG1c/oEvPVyeHUZwG96JzJuPtGmOu3sNz21ze7DzeBpRLmhHmh9upolI2EPSkvl3SCTQDzW03ood1pVV9SURZaHmg/ltadXkFzQ0utO21PF7U7wZtA64MVmO48J6BxC8B0zKN+YgfQvT7YcpN4P3nVhdkGtFmxGE+bOq/gOwZFf/JTE225UEisnzwQLMQ99ACQ1/wBrOavBBERUcoi77tZrDzvPCisllDY1tEUH0RKgar90FYwbLpPC3fXTOjPi10NY9a/7OlXTczxzj/nNFJUayisAcZDf9fSOd/Kdl2iZ9h0m/uuY+BahrruJHe/bCzKdatgPbFyTV5GricnWOaogim5Hid3+02wv1eX++6k/QAsvOuw4r6hlMWtpbbQUNLFgy1g+aa6Fhq4DhYbQNsMr99eYqkFm8B8bMvSILKrTRgzDRTkL0AfAics4/TFlt4v9D25QBa3ljSYWw/SPbHTKzgW+l97N4TQAYvOV7K8GrAff+CFdPnmOLzohx5ERHTpuA8K1xrQd/0tBufHfg8rulJJl57bSLFWATaaF9IKlF09tutEJ3OoyIGiou7ke0dS9a5eYFwQ1fuY8vcmwTKqdzYT1PXk/b267NZqy7LQma2E1NP7u29kby6j/aJT8z19gE0s41bINRq4Dk4MaNJ4MuMixWAzqmVJw9yMoiXKY7fQqVuyslgZsjuteMI6G5r9JZS6d3arFTSkYGtqzQRGeWIX6s5+2QGS/Tn95JX9VM18ZMoLDSxJXiUyrSGPNkzxJusdrxtoh+knD2QxDz3kp0LmIdLLPSIiCujjvhu47x9Jy56a8HVYGzYd8FUYA5XtuPUve/pV08fxButGJg59s+y6RM+w6ZLpW1j2utnSOMveXFZ0T4+4b4SZvoXldgW1U7cLbcjrYirTKzDKQMMNVsdIusGm2LJ0R/wGxrQS7t1FxXlXUsnrTlvAppzWJ69P/zWxVc15R9AbWcqdOjBm0jux3Wou5MmIGFz1l1dR75aGf14y6rxKah4l4d2J/vSXBwHeQ4+KrxDKXs8HCqru43awizAREaUi0X13z+k+Kz8o1OagyQ8tzUOYM05rwLDpAHBaQ84dWVKuMMatf9nTr5okx3vqdJ/dlRspVA+xTRweaVi+mU0hXfVTazSWQn660SfqvhEpi1tLwObDltOFNnGo6Rntq3MjIo8YFCZyNFp5xKRd3QLkEeacUcFUIziJI4a68wKjOrnry6OBRQnZP2dkqMgR3Vx9jzoVxflceTSqwGf0kVfK0fzcUdmCy4ZLmFd9jVAWcrwnhqX5rg/FKG595IF6f5zP9i0rf440Wi0R0RV37qPRxt53/f83y/7yxj+qubytYdOFkW9DRK9/+dPPw7mNRht7vP7/7XqQUIdQjQ4r1uWGTFf+Ly8fqKuIwuphoiQjkiZYJkE9Nn5/R+vCRqMNua56eRF/3/CT7nluHdjLf/XnBa6DqFjlHAwzGu1ogk33hiAHCt6Xzw4CggGpI+xL4Jyg5Cc4fP+8k11uBgo3P8VFFbZ/iTgXrZgHIdtKlFeOfpZVS5ZXlvKzoj/Xvhb8k/9z5C+akBL4rOD2Q2+IisBUDEIhn1cioivu/INNK+K+65SHvjJQLnOlMjNwrx88PVi+qMqZ8PXHI330zjPYjDreYL3TWTb0nIfUJ4ZI99d3/OmhdRVPRD3MkyCQTLJMgnps/P6O1sUFm1bgnhV+DYWf7x75AZtc51X/H9x+3LUxWsMEmxnLsiy5tVPFMAycnZ2hWq3KSURERDQGcrkcdnZ2oGlj2BWLLqWDgwOsr69jf39fTiIa2O3bt1EsFlEsFuUkugCTk5N48uQJJiYm5KRYqb+zSURERERERDS+weZpDTnpt39G/bMakQI/6yFNQwzUM7TLlldERERERHTljW+wKf5epXKSf3NoxMSf9VBNj+J+7HWELlteERERERHRlTe+wSYRERERERFdWgw2iYiIiIiIKHUMNomIiIiIiCh1iX/6ZH19Hb/3e7+H97znPXISERERjYEf/MEfxMbGBt7whjfISUQDabfb+PCHP4x/9+/+nZxENLDV1VV8y7d8C77/+79fTqILMD8/jy9+8YsD/fRJ4mDz+7//+/Frv/ZryOVychIRERGNgd/+7d9GLpcbqMJApPKnf/qnODk5wbd8y7fISUQD+8xnPoOv+qqv4oOxS6LdbuOVV14ZqOxIHGwahoGzszNUq1U5iYiIiMZALpfDzs4ONE2Tk4gGcnBwgPX1dezv78tJRAO7ffs2isUiisWinEQXYHJyEk+ePBko2OQ7m0RERERERJQ6BptERERERESUOgabRERERERElDoGm0RERERERJQ6BptERERERESUuiGDzS5qsxlkMopptoauvDgAoIWSIr21pNiGN+VQOxUWDuVsWzUttYTlIvY7k0FpT1g0dPkSxC26y+Sq8lEr1vXti7CMKs/2SorPcuyV1OsQERFdGv6yOVhORpHK0LDyc+B0gbK8jVv/sqdfLd1qrnesgXMVY68krKuoVw6T7kuTJq+epqgPepN7LBH1WG87CZaJ2B/3++fPS/9k14OT7O9VYx+zLw4IzUvxGpDzKip/7PMXfh+U0+Vt96ZgvHI5DRls2vRdC5YlTh0YqGBKcdPrVitolHXoRxXcFTJpfktYf1cHZgx0vO0dY2Va3EoUDcaJvD9N6NuFwIkN7rc91ReEhU5ryGWmsLnU8S3T2WijIN9sZM66lXzTt24TBfWFeFRBIfTik+yVkFlsyHOJiIgukS5qswW0N9wytIn82lTiSlJrSSxDOzDa/rJ82PSeFkqKMjVu/cuefqXslTC1lkdTrIclfeB+WkNusd2rH+7mUbkm1MOGTQekeqswPVpBVlhMXfesY95bQlWPlbeTYJmQ/TleFfam7K+fupNYD47f36uii9rsFCpH0uyFunTsHRgzAMqGF5u0lqZQQS+/E12bWwXFfdC+XwbvRIrzsKujsRgTh1wWVkLVatWqVCrS3I5lzMDSd6XZlmVZu7oF6FbTN7O3fLMMC2V/qmdXtzBjWB15fqympUOzjBN5vmV1NjRhmxH77WMvp22o96RZFtPkZeX//fzH7+xPWbcg738gH5uWDliAZull8ZiIiIiiaZpmHR8fy7NHJ1CGOeWxU/41y1CWcdpGx7JODEtTlYluuTdsuqBZdstgYV/i1r/s6edkf3/funHjhjw7Zao6lVDnOzEszb1u3DU2NO98Buuc/nrgsOnJ8j1J3TO8HtuTYJkE+yN+D9WS7O/oFItF6/79+/LskbCvFVgo65aO6GMWryvLCvkeRrLvcfqGYWly/p8YljajW7oingjuU4LrIEUTExPWq6++Ks9OJJWWzcROH2DzSEdpAZgv6sB2Pdi6d5k4+2uIT4EE81vSEyLR6QNsHmlYvqlOn3/OgCYff7GOZtlE5Wb005C5jQ4s6xj3ZuUUIiKiy6P7uA3MzEET5mWv573yf36rA2OmgYrTGtdaKqBRbtplq3kIE3loYs8mbQ7a0SYenGL4dNdeCQU0UZd/Oz5u/cuefqWYODwC8tfFOpWGuRkTmw+7wPQKjnd1mGt37XrVaQ2FNcA4qWMeXZhtQJv1XYXQ8kDjxRYwdDqNv2W7xXyrJCdIWri7ZkLf7bXsdh9uwpxZxq3EPTAd129hue2PA7oPN4GlEuaEeaH26mgIrauX2YiCzS5qdxpAueRrZm99sALTnbdwz1fAjJZ9cWhLt3xdGRqLwf7Pvu6t5iFMqZBMTFUIiKY15NGGKRUI81tN6JHdaeexEhbgEhERXTZ5zVf2QhPL1SxWntdhrhVQq5ZQ2NbR3BJqDnIZPK0hL/4/bDq6qN1pw3gupFNg3PqXPf1K0TDnO1g74PM49crCUg21mxVgo+mriPsDVTl4HD4dRxVMBeqUwXfzgnVPuSukico1eRn51asEy6j2R369bbuQwv6Ov+zqSqJuwd1qBY0ZA/fE1+1g3+MgvgMb14UWAJDFraU26l5X2i4ebAHLN9VRR+A8LDaAtpngcy5eKsFmIAPc9xTFAgMt1LcBvejOy+LWkgZz60HKGaX6Atrvi8itkIH+zwn6oQdeqE50QfVjHvVdp+C9Yl9mIiKigAWnV89aw9dicB661QI2l/xBCY2rLFYeGtC2K6jAQPO8H8wneUdSWfeUxyVRvY8pfy8SLKPaH1+9XP3OZv/7+7To4sFWsOEKsIP2Anp52cxXMJUgPsjeXEbbbR0/fYBNhLeQBs7DiQFNGv/mskol2BQzoLOh2V8C6Slht1pBQwpMp9ZMIPWM8n8Bm2UACO8KG0mbg3Z0CFOYlV09lo41hDYHTdFy6Tk10Q5r+XQL3pjutERERJee/PTd9JerovZjqdSTymC77BQMk35aQ2FrOTooiVofY5B+pZg49B2s3b1VSc4XxbVlPvIvMWw6XXFRr8e5Xf8d888ZybqzT9/CcruC2qnbhVYRyIaZXoFRHo+u3KkEm6Ls6rEdKPlG6XKeBnij0bmTPaLTKDPKfR8kdlQolelbWB60q+/0LSy77xIo+LoUK3jdae+E3UmJiIgut+z1fKDi333c9r9ms+d0n5V79age2pqHvfejhkzvPtyEKXY1XGwAaPRGmo9Z/9KnXyka5mbkgM/Eoa/y3/W6z9rdad26pd3d1h8c2oGq3dtu2HR6GoS9m5m9ng8+UEssi1tLwObDltOFNnGo6Ql0576M5BGDwvQ3Gq0zYmrUiGkuxUh1SUbRUgsZmckZpSww+mtgvxUUI5xZlrvfwjGqRkoLfK4tOPpeyP64nyHnj8M/wi4REVG0cx+NNlA2OiMxeuWd//9mWSxX5VFA5W0Nn+6jqI/ErX/Z08/D+YxGGzw/ch3I979T//KuM7keKp/rYdMT1VtD6no+IfVYnwTLJNgfjkarIt+fhJTAiMSu4PdOvo/5SZ/hxgqh8UTIeZCvyREbZjTaEQWb/kArSab7bo4JviRqEV9AJ3CzP8febzuQU0yBi0m1vPw58sXhn59k+6p8DAamPfKNloiIKMr5B5uWV867ZaBcifKXY3KdQCpDQ8rPwdMFcgBhWQnWv+zpo3duwaZb73GPVQ72pCAh8BMV3gN8KOpwQ6b70uTJXTa8rtcTUY/1JFgmQT2awaZKWLAZVsd3Sd/DyLyXP0Petvr/4HWl2s/RGSbYzFiWZcmtnSqGYeDs7AzValVOIiIiojGQy+Wws7MDTRuDrlc0Fg4ODrC+vo79/X05iWhgt2/fRrFYRLEo/yYRXYTJyUk8efIEExMTclKs1N/ZJCIiIiIiIhqfYHOvJP2ciTQNMgAQERERERERjcT4BJsLdWkkW2l6tJJ8uGAiIiIiIiIaqfEJNomIiIiIiGhsMNgkIiIiIiKi1DHYJCIiIiIiotQl/umTf/2v/zU6nQ7e//73y0lEREQ0Bt7ylrfg3/7bf4tnnnlGTiIayG/91m/BMAz8yq/8ipxENLDl5WV853d+J8rlspxEF+Dv/J2/gy984QsD/fRJ4mDzbW97G379138dr3vd6+QkIiIiGgOf/exn8drXvhZ/6S/9JTmJaCD/43/8D/zZn/0ZXv/618tJRAP7/Oc/j7/yV/4K/tpf+2tyEl2Az3/+8zg7OxttsGkYBs7OzlCtVuUkIiIiGgO5XA47OzvQNE1OIhrIwcEB1tfXsb+/LycRDez27dsoFosoFotyEl2AyclJPHnyZKBgk+9sEhERERERUeoYbBIREREREVHqGGwSERERERFR6hhsEhERERERUeoYbBIREREREVHqhgw2u6jNZpCZraErJ+2VkMmU0JJmd6s5ZDIZYQouY6+bQWlPTrA/L1cNfFoIZ/98n+dMqn0GALRQUqS3lhTb8KYcaqfCwpEU+7QUyAEgaV65nDxLtKySYr/cScqL4H5FH0sw7+T8Cj+vwc8KHpe9jLxNeOcyeB3BSVOtQ0REqfKVT33ed2PXdcpsZwqWI1Hp/jTfJJZ7cftw2dOvFKmuoqhzRPHXKcLqE4OlB+srvcmuh0TUs5xthW/D/awk12yCZQJ1xt7kfkfC9yWsXnUV2ecseLzyuQxeKz32+Qjem1xyurzt8c/3IYNNx1EFhdBMdNmZN7WWR9OyYLnTLlAIycDGYtTJS07fFT7PsmBZHRioYEpxk+pWK2iUdehHFdwV9ml+S9xnHZgx0PG2d4yVaXErYbqozU5hc6nj35d2QQro+sur1lIGmUX4lu1stFEYoNBJnFflprScM23NCwvZX6ACpGV386hcCx6HX395AJio3Ax7gCBroZQpoCHPJiKidJ3WkFtswzgR7/8Jy/bYdbuozRbQ3nDL1Cbya1NC+RCXDgBab/vi9GgFWSTYh8uefsW0lqZQybt1Crv+FF6Jl+yVfHWKzkYbBemhwlDpCK8b1Rd6iwTrWRYsq455ANnVY2l+EzoAbeMeerWrmGs26TK+emxvOl7tbSXJ8Vxddp29ciTPd65D9PJPeS3ItgqKuqt9j1LVRwPXya6OxmL/9fpLwUqoWq1alUpFmtuxjBlYelm3AM0yToSkXd0CdKvpLrmhWZgxrI6wiEda1trVLczolj4DC2Vvrvd52oZyKwrO/u3K8xWfaVm+5Ztl+bMFu3r4sUQ5MSxNzifF/L7zKnActshjCEieV50NLdF2oz7fv43gee0nD+xt6ZYO+dpoWjr8x9QswwJgaWVdfS6IiK4wTdOs4+NjefbIBMsBsayx79G+9N1efSJ63WBZYMllS1y61bT0mHIgbh8ue/p52N/ft27cuCHPTp+qDiXUxzobmlQXtfPCzp9gPcN//odNl68tlf7PTbMMqS4Uf80mWiZBPTb+eEarWCxa9+/fl2efC/tagle39J0z1XUYyamLbhiWJufniWFpTrzTu7bCrpME53WEJiYmrFdffVWenUg6LZvFOprlqJalLh5smdCWbglPXQQL92DMNFD3RfxzuPfQgLatehIwIqcPsHmko7QAzBd1YLue7tPBaQ15mKh8UNrq9AqOvdbR/vKq9WIDKJeEJ14981tyS+N5aqG+rcF4Tv352dXjiH3rLw9sJdR3dZhrheinPrP2k6jj5+bkFCIiSlUXZhvQZjVhXhZaHmi82AIwj/qJAW274ty3WygtNqDvHmNlOm5doPu4DczMwbfE9bxXdselx4vbh8uefsWYhzCRhyb2JNPmoB1t4sGpXa9olnt1rG61YLc+bc0DMHF4BOSvi7UKDXMzJjYfdlNIH4HTGirbGoyHYoslnY9luwV7qyQnoPtwE+bMMm4l6tEouH4Ly23/vaf7cBNYKiFRjXSvjkbZSNiT8nJJJ9gEML/VhB7anVb1JRXZN8f2Y2nd6RU0N7TUutP2dFG7EwzSWh+swHTnOQFNRXk8g3IL1oLQB1tuEu8nr1QFTdrUeQXfMSj6k5+aaMuFQmL95IFgIe6hBzC/yps2EdF5ku/lvjJregWGc9+uLRXQKDd9XfQi1wWAvOa/p2v+4DI2HSYq1+SyzF/niNuHy55+pUgPD+yH+D3zzzmNFNUaCmuQAjUNc76V7bpEz7Dp6rqR3M23sShfb3I90GbXSVXBRfw1m2iZowqmpH0NvAOb4HiuouzqirIRx5PXAPGd1rgutACALG4ttYWGki4ebAHLN9Xf18B1stgA2maCz7l8Ugs2gflkLUt9yq42Ycw0UJC/AH0InLCM0+dfer+wvg3oRXdeFreWNJhbD9I9sdMrOBb6X3s3hEQX6ugly6sB+/EHXkiXb47Di37oQUREl417365s62jKZc3Iqd5ts9+fozHkNlKsVYCNpiJQGzFF3cj3DqTqXTzluB9ynVSU5JpNsIzqnU35+5fgeJ5K2wXfeCTNfAVTCerx2ZvLaLu9Dk4fYBPhLaSB6+TEgCaNJzMuUgw2o1qWNMzNKFqiPHYLnfx0zpbFypDdacUT1tnQ7C+h1L2zW62gIQVbU2smMMoTu1B39ssuaO3P6Sev7Kdq5iNTXmhgSfIqkWkNebRhig8evON1A+0w/eSBbDQPPYiIaDDyvTy8zJLKjCTryk/6zUP4lohLTyBuHy57+pVyJJ2/UxNt8X9BMB9MHPpm2XWJnmHTU7RXRwP2a110CZWbvqB7/jnD684dafoWltv2awN2F9qQ18VUpldglMezi3y6wabYsnRH/AbGtBLu3UXFeVdSyetOW8CmnNYnr0+/NKLdgy0TmjdinTt1YMykd2K71VxIC6YYXPWXV1HvloZ/XjLqvEpqHiXh3Yn+9JcHAd5Dj0poIURERKOmeiBqV9DFFpuW033W/7A6ft3s9Xwg+Og+bnuvfcSlx4vbh8uefsVoc9DkBxLmof/9uVOn++yu3Eiheoht4vBIw/LNbArp6Yoaj4MuVvZ6PvgQK7Esbi0Bmw9bThfa/q+dsewmL48YFCZyNFp5xKRd3R7FyTcKnDMqmDxyqrOsbxvKUbLc9eXRwKKE7J87Ap77GVEjSylGs1PvXxKKkfcs1Wf0kVfuaGWqEfcUy4ZLmFd9jVAWcrwnhqX5rg/VKG/J80C9P85nh+VB1DknIrqizns02sC9Vi7vfP/b922vLIhbN1B2yCOQx6UnGN0xbh8ue/o5OLfRaN36jlfey+fX/79dDwq71hSj3g+Zrq6LiMLqWTL5uEQJrtkkyySox8Yfz2hd5Gi0PfI9w1Ken+CowSJpG24d2Fte3l7IdSJ/18/ZMKPRjibYDAmALO/LbwcBwYDUEfYlcE6Q+guoEr5/3skuNxNdJL7PDNu/RNwgSphCtpUorxz9LKuWLK8s5WdFf677cyPiFPfF9VICnxXcfugNURGYei74S0tEdBHOPdi0evdiexLuu6oyfVf3LxO2rqf3YBHytmLTE1TKrQT7cNnTR+w8g81AHUoo+4P1TmdZYRl/nSKkPjFgemhdxBNRz/JRBTiuJNdsgmUS1GPjj2e0Lm+waQWvw8i8lLch13nV//eus94U3I/zM0ywmbEsy5JbO1UMw8DZ2Rmq1aqcRERERGMgl8thZ2cHmjaGXbHoUjo4OMD6+jr29/flJKKB3b59G8ViEcViUU6iCzA5OYknT55gYmJCToqV+jubREREREREROMbbJ7WkPP9jIY8DTKozRACP+shTUMM1DO0y5ZXRERERER05Y1vsCn+XqVykn9zaMTEn/VQTY/EHxY+Z5ctr4iIiIiI6Mob32CTiIiIiIiILi0Gm0RERERERJQ6BptERERERESUusQ/fVKtVvGFL3wBP/mTPyknERER0Rj423/7b+P+/fu4fv26nEQ0kE9+8pO4d+8ePv7xj8tJRAP7oR/6Idy8eRM/+IM/KCfRBfjrf/2vD/zTJ4mDze/4ju/Apz/9aXzZl32ZnERERERj4C/+4i/wpV/6pchkMnIS0UD+7//9v3jNa16DhNVJokT+4i/+Al/yJV+C17zmNXISXYDXvOY1ODs7G22waRgGzs7OUK1W5SQiIiIaA7lcDjs7O9A0TU4iGsjBwQHW19exv78vJxEN7Pbt2ygWiygWi3ISXYDJycmBWzb5ziYRERERERGljsEmERERERERpY7BJhEREREREaWOwSYRERERERGljsEmERERERERpW6IYLOL2mwGmaWWnGA7rSGXyaF2CmCvhEymhMCSeyVkMhlhUiyTiLMvwrZy1a5/iWpO+qzeVNrzLarYr+D2iIiIKI5UPofVGUL4y+5gHWGY9Ph6QbBuodzWaQ05IS1YX2ihdKHpV4yvjubUM5OKXTcuL6PS/Wm+abaGLuTP90/itlpLYlrMde1uW5UmTcmv6wTH8tSw8ysQKwTyMXieesK24eZzXL6rrtXxMUSwmcXK8zqwXVdmbuuDFZhlAyvTcoqttZRBZhFoWhYsZ+pstFHoN0NPa8hlprC51PG2Y1lN5NemgoVauSks05vqC71FVPsVuj0iIiIK1VqaQiXvlr0dGO2CogIfYq+EqbW8Vx53NtooiBXdYdORrF6g7wbTLauOecCuHF6rABtuHcSuL/QqlV3UZgtoX1j6FXNaQ26xDePEOQ+7eVSuRVXyBbHrxuVlXDoAaL3ti9OjFWTdRWYMdOR0y8Lxqr1Et5pDYVsPvW671Zzvum7mK5iS66dDX9dIdixXXhe12SlUjuT5zr0NvXMpn6d4LZQyBTTKzdh872wg+XV+GVkJVatVq1KpSHOblg5Y2kZHOV/fdf7d1S1At5pusvy/oFmGhbIqRaVjGTOqz7cs68SwNGiWcWL/29nQ4rcbsV/29kLSiIiIxoCmadbx8bE8ezSkctiynHJ2xrA6brnsS7fLdLusVpXvTUv3lh82PUm9wN6GV5dRURxjsyx8rqJe4fvcUaefg/39fevGjRvy7JEI1hHFc2TXPX3pu7p3jUWvmyAv49Kl60tJuP7DBPbTV/9UXNfSfsWf/wTXdZJjGbFisWjdv39fnn1u7PsTLJR1f0xjqb/30eQ8d67VwLUQku+XIAaZmJiwXn31VXl2IkO0bALAPO5taDC3Hvgj+b06GtBREp6giFovNoBySYjie+a3LFhbqhSF0wfYPNKwfFPxjGV6BcfWcWjLqkrrxQa0jXvK/bK3Jz55ICIiolDmIUzkoYnlsDYH7WgTD06B7OoxmmUTlQ/az+u71YLdUrA1D8DE4RGQvy6W7xrmZkxsPuymkJ6SaQ15iNtsob7d+9zu4zYwMwdNWCV7Pe/1Cht1+tXShdkGtFnf0ULLA40XWwDmUT8xoG1XnB5yLZQWG9B3j7EyHbdufF7GpadFm9V82+w+3ITpfW4WK496raCIqVPTMJbt1uOtkpzgnJNl3OojxuhxWjRnDHQSthJ3H24CYfHJGBgy2ASyN5e9gsPWRe1ORNCmvFkMSFWQRdkuBPpB97rz2PvlL5iIiIhoYFLl3A7OeuafM6BtF1Cq1lBYA4yHYuVLw5xvZTs46Bk2Pa5eYGssRr0/NY+61cHy1pSTVsHcib+7IvKav0KpSXky6vQrRq6n+eqT0yswyiYqN2uoLdldFMVzEbkuEuRlXDpMVK7J14vU/fGoginpmhNf08quHsPaBQpO2tQjQ9l11X03s7Ctoyk30gx9XSPZsVxh2dWVkDjGkdeAiHdn1eyu2A0A+vPBc2oL5vvUmgnzkSkvODaGDja9L7bzZDKytfGiKfqwi0+HgoIv6l7Z9yCIiIjO2/QKmhsaGmsVYKPZV2+kVCSoFwTfbRN6TTnjRhw+76YZOLwWrNjT+ZnfakI/qqCiCsJGLvi+nf99vJB3NoX9bC1lkLkz13sXcLaiDPKyq8f2uidzqMiB4rDXNZDsWJ5m2wUU0MvnZr6CqZiAs7Fov+dplIHGYvCc2hT5vqsDXov9+Bk+2AQwX+wNFNR9uBk5MJD7ZDGVCF2bg4Y2zFQy396v9mPxMplH3TvZTehCChEREcU4OoSvtD810Rb/FwTrBSYOfbPsHkg9w6YPz+5OZ+Ce13o2j/quDnPtbq8i2Tb9FVBTypNRp18x/nqa6rpxBeuHsevG5WVc+tDsbthiq1d2tQljpoFK2AOM6VtYTrt7OMUrN30B/PxzhtTTU8HpOruy1YEx00g+oNDCPRhjfI5TCTbtTGigvtfC3TUTejH6uYcYnMq61VzCpui4L5jdKtlPS+R8USogiIiIaDCqB8Lmof9dp1On++yu053WK7M1zM3IwYGJQ6/n1LDp5yN7PR8IuLuP2947dqNOv1pUjRX2AwSx3tlyus82ne609hUQv25cXsaln4/+67aUvuz1fPDBQwK9hwhZrDw0oB1VUAh7iKAgdwMfG/KIQWHUo9H2eKM2BUZWCo6UZbmjbalG9ZJHfIpzYlhaYETc4ChP8aNz2VT75Y2Q1+++ERERXSLnOhptYGRNeSRN//92HSB85PrOhuavYwyZHl8vkEeQVHDqIPIok2HHGBitf+Tpo3eeo9EGRgGV65e+/6VzEbdubF7GpYeMJCpKOhqtfJ3GXcdy+rDXdZJjGbGLHo22Rz7PluJaCJ43v5A839Wl2CIk3wPX6vkbZjTa1ILNwJdaFJJJXoDqTcFlknGCS2GS9yP+yydwTr5vSrouERHRJXXewab4sFYuS4MPd51lhWX89YRgHWGY9Ph6QUgFUeYEnKpjtPnrKHL9ZPTpo3WuwaYl19GEirmq8WG399Mnket64vIyKj0kUBAlCDYt77sRvp/+9FFc1wmOZcQud7BpBe9tkec1PM/dc2mnBeOZsGvgvA0TbGYsy7Lk1k4VwzBwdnaGarUqJxEREdEYyOVy2NnZgaZd5fFK6TwdHBxgfX0d+/v7chLRwG7fvo1isYhisSgn0QWYnJzEkydPMDExISfFSuedTSIiIiIiIiLB5Q02T2vIST878rT+1g8REREREdG4ubzB5vQKjqXfCOJv/RAREREREY2HyxtsEhERERER0dhisElERERERESpY7BJREREREREqWOwSURERERERKlL/DubN27cwG/8xm/gq77qq+QkIiIiGgN/+qd/ir/6V/8qvvRLv1ROIhrI//pf/wv/83/+T9YPKVV/9md/hi//8i/HV37lV8pJdAH++3//7zg7OxvodzYTB5svvPACPvvZz+IDH/iAnERERERj4Fu/9VuxtbWFa9euyUlEA/n1X/91/ORP/iT29vbkJKKBLS0t4bu/+7tRKpXkJLoAzzzzDD7/+c+PNtg0DANnZ2eoVqtyEhEREY2BXC6HnZ0daJomJxEN5ODgAOvr69jf35eTiAZ2+/ZtFItFFItFOYkuwOTkJJ48eTJQsMl3NomIiIiIiCh1DDaJiIiIiIgodQw2iYiIiIiIKHUMNomIiIiIiCh1QwabXdRmM8jM1tCVk/ZKyGRKaEmzu9UcMpmMMAWXsdfNoBQY2Mz+vFw18GkhnP3zfZ4zqfYZANBCSZHeWlJsw5tyqJ0KC0dS7NNSIAeApHnlcvIs0bJKiv1yJykvgvsVfSzBvJPzK/y8Bj8reFz2MvI24Z1L33V0WkNO2J7qM4mIKC1S2aIoI6L4y4Cw+/9g6cHypTfZ5UZEuShuq49ypVvNBcpUr94Ruv6w6VfJ0349+c+1b/KuqwTLBOqMwc+LP56niDKmkfI58lq0z30w39xtuNsOO3eqOu74GDLYdBxVUIi9udkZPbWWR9OyYLnTLlAIuXAbi/KJHYy+K3yeZcGyOjBQwZTiwuhWK2iUdehHFdwV9ml+S9xnHZgx0PG2d4yVaXErYbqozU5hc6nj35d2QSp8+sur1lIGmUX4lu1stFEY4OJMnFflprScM23NCwvZX5oCpGV386hcCx6HX395AJio3JQLcMlpDblrFeS9Y2wivzal2BYREaWhtTSFSt4tA+zyLnEwtFfylQGdjTYKYlk5bDrCy7L6Qm+RYLlowbLqsEu7FkrXKsCGW65HlCunNRTWTGlmF7XZAtqh6w+bfrXwegIADcaJvL4F69EKsv0s46vH9qbj1d5WkhzP1ddCabEhzbO/dw0vf/q8FgGnjuxuwz3/UJ67zgZQuZZOTHQRUgk29bIOc60QGdh0qwVUYKDjy1AAC3VYu3owsJzRoc80UJCDnFRksfK8DmzXpRPXxYMtE3qxjlIZaLyY8mefPsDmkYblm8IXGVmsPDSgHW3igZN/feXVXgmFbR1Nadns6jGaZROVDw57DGF5Fa+15HyJfAGofRydDS0yf/vKAwBwHhBEPfToPtyEOWPgnneTnMe9mP0gIqIBndZQ2dZgPOfexe3yxNx6gK7XciI+FHVafpZa9t93GtA27nllQHbVgO6VlcOmp+TURBtiuT6PUhloP5bLoi5qNzeRL0s/ObN3F5UjHYZXwZfKpWHTrxJeT3QBWksFoKxLc00cHgF6sXct3lrSYD6SHyaFcQLNGQMduY6skL25jHH+sapUgk0U63ZgE9qyZAdx2tIt4amLYOEejJkG6r4nN3O499CAtl04vyd0pw+weaSjtADMFwcLsCJNa8hDEQBOr+DYax3tL69aLzaAcskfkDnmt+SWxvPUQt1XKPhlV48j9q2/PLCVUN+NfuiRXT2WnvyhjxsDERH1xTyEiTw0seePNuc9XJUfinoPGbfmvcpc/rp4x9YwN2Ni82E3hfSUOOV6b5st1Lflz7WPbXOpiXuzvtnoPm4DM3O+imT2et6rfwybfqXweqLztldCAU3UAz/1qWFuRmyUcuqts0lCQiHQlOqkYboPNwHhQce4SSfYBDC/1YxoWVJ9SUVZaHnFk5vpFTQ3tGAr1tDsJ1RykNb6YAWmO88JaCrK4xnUPOondgAd3g+7n7zqwmwj4cU9KHVewXcMin78pybacqGQWD95IFiIe+ghCTwlJSKiVEmBkF2Z7pl/znmoXK2hsAYYD/1dAed8K9v3/p5h09VlmdwVrrEol3ViuT2PutXB8taUk1bB3InczbCFu2t5ofVRktf8FU5NyrNh068SXk/2a0PX5G3I9eQEyxxVMCXta+C9wwTHc3V1UbvTDqkjZrHyyEITbv5M4fB5qQuyktP9FoD+fFigGTx3U2vmWDeOpBZsAvOxLUuDyK42YQzZnTb4xXb6/EvvF9a3FU3iTveM1Eyv4Njth72r9y4qud//BUmWVwP24w+8kC7fHIcX/dBD4Ly/iY1mwvdtiYgode5D5bULuh8ryjK5whh8x04Yp+G0hpxT0bTTDBxe81fIW0sFYFd6JYRG4ym4nlTv9Pnf+Uu4jOqdzQR1Pfl4riq3N4L6GrLHJKnMuu/WdjB3RxGsSxqLU6jAgFGOGpdGce52dWC7kmp8dZ5SDDajWpbs5uZAS5THbqFTt2Q57zQO0Z1W/GJ3NjT7REpPKrrVChpSsDW1ZgLSQEGpWqg7+2UHSPbn9JNX9lO1NJ92JMmrRKY15NGGKX4xvON1A+0w/eSBLMFDDy/Q7Dw1N00iogtxdAhfCXVqoi3+LwiWZSYOfbPse3/PsOnDU40FYJdBd+2KpNsNL+pBbNv015lMKc+GTb9Knvbric7HaQ2FrWU0w+qIe3U0IL4r7cYqMQGh03V2ZatjN6QlbWhauAcj7S7b5yjdYFNsWbojfgNjWgmdF9xLYTdjrzttAZtyWp+8Pv2+UZ2cvtbe6F/u1IHh65M9HPWQ55CCq/7yKurd0vDPS0adV0nNozTwAEX95UGA99CjEiyE9krIOCPSMtAkIhohbQ6a/NDRPIQ5s4xbQktOYQ0wduWHyqqHjiYOvUH2hk0/H60XG76uiO5D7CmnZ0/2ej4QQHUft71XV4ZNv1J4PdE56T7chCl2M15sAGgM9CsPol7XWXdw0AQ98QTqRpYxYCVUrVatSqUize1YxgwsfVeavatbACxAt5reTHtZ/7zesr5t7OoWZgyrI8zqrQ9L2/CnhAvZP6tp6UDvM04MS4NmGSfycu7+KfY5sH9JOJ9b9m1N8Rl95JVlWc1ycNnOhqZcNlzCvHK3LR+DUsjxnhiW5rs+7M/2n9fkeaDeH+ezxWWdzw0eIxHR00HTNOv4+FiePTLNslgGyPd6//92uSXc86WysbOh+cveIdPVZYcorFwUBMoVu+wJq6fI+yDngbt+b3vDpo/e/v6+dePGDXn2SPB6alp6WH3Vk2CZBPXY+OMZrWKxaN2/f1+efTEC9XT5vDh11tD8Cjn3gTptyLkLfP75m5iYsF599VV5diKjCTZDAiDL+/LbQUAwIHWEfQmcL2HYTTwofP+8gKfctPdV9XmWpbigIvYvETeIEqaQbSXKK0c/y6olyytL+VnRn2tfC/7J/zlygSGkBD4ruP3QG6L0JVbtBxCe/0REV815B5uBMk+4VwfrCcEKm78MCLn/D5geWnZ4IspFkfcANXiMMjlAsfUejkKub6SSPlrnGWzyegoJRnwSLJOgHht/PKN1uYNNK/C9i87P8HPv1k3tNGmb3hRzPs/BMMFmxrIsS27tVDEMA2dnZ6hWq3ISERERjYFcLoednR1o2pUdr5TO2cHBAdbX17G/vy8nEQ3s9u3bKBaLKBYDvztCF2BychJPnjzBxMSEnBQr9Xc2iYiIiIiIiMY32DytIef7GQ15GmRQmyEEftZDmoYYqGdoly2viIiIiIjoyhvfYFP8vUrlJP/m0IiJP+uhmh6F/XjrObhseUVERERERFfe+AabREREREREdGkx2CQiIiIiIqLUMdgkIiIiIiKi1DHYJCIiIiIiotQl/p3NxcVF/Nqv/RqmpqbkJCIiIhoD/+W//BdMT0/jK77iK+QkooH8t//23/C5z30Os7OzchLRwH7/938fk5OTeN3rXicn0QU4OTkZ+Hc2Eweb/+pf/Sv8/u//Pt73vvfJSURERDQGFhYW8LM/+7N8cEyp+Z3f+R186EMfwvb2tpxENLD3vve9+LZv+za8853vlJPoAnz7t387vvjFL4422DQMA2dnZ6hWq3ISERERjYFcLoednR1omiYnEQ3k4OAA6+vr2N/fl5OIBnb79m0Ui0UUi0U5iS7A5OTkwC2bfGeTiIiIiIiIUsdgk4iIiIiIiFLHYJOIiIiIiIhSx2CTiIiIiIiIUjdksNlFbTaDTEYxzdbQlRcHALRQUqS3lhTb8KYcaqfCwrGczxCm0p6YHrHfmRJa4qLKfet3f4iIiJ5GUnm7JJew0brVXGT5PEy6P80/2XWGhHWF0xpygXVF/jpJrirXjkadfsXslYTz0Gd9LHbduLyMSg/WPb3JrfP6Pt8/idvy1TsV9enRX9cJjuVptlcK5Lufncfh94K4fFZdm+NryGDTpu9asCxx6sBABVOKQqVbraBR1qEfVXBXOAnzW8L6uzowY6Djbe8YK9PiViLslZDJFABxn04MtBflm4Jqvy1YVh3z3hL2RVBA07/Mbh6Va6qLiIiIiFytpSlU8m4Z2oHRLgTK4lB7JUyt5dF0yt7ORhsFsaI7bDoAlKXy3ZnqC71FIusKpzXkrlWAjY53jHN3xPpBF7XZAtpeehP5talzTL9iTmvILbZhnDjnYTePyrWoSr8gdt24vIxLBwCtt31xerSCrLuIr37bm45X7SVaSxkUtnXvurWeP8TUeV/XQLJjeSq1UFpsyDMTaKGUKaBRbsbmc2cDya/rcWAlVK1WrUqlIs3tWMYMLH1Xmm1ZlrWrW4BuNX0ze8s3y7BQ9qd6dnULM4bVkefHalo6kuxPxH4Lovaxs6GFphEREV1GmqZZx8fH8uzRODEsDZplnAjzhPK9s6FZ8KXbZbNdttp/axtiTaBp6d7yw6YnKcfj6wqdDS1YXxHrMIq6kO9zR51+Dvb3960bN27Is0ciWC8Tz5FdB/Sl7+reNRa9boK8jEuXri+l2Pqtqh4r7uf5XNfyNi9CsVi07t+/L8++cM0yLL0cvBb85Dx2rs3AuQ/J5xPD0iK3f/4mJiasV199VZ6dSCotm4mdPsDmkY7SAjBf1IHterpR+14dDdjbD1ioS08S4rRQ39ZgPKdeI7t6DGtLnUZERPTUMw9hIg9N7JmkzUE72sSDU7scbZZNVD5o1wS61QIqMNDZmgdg4vAIyF8X21A0zM2Y2HzYTSE9RXkt2NJzdAgTQPdxG5iZg/irptnrea/+M+r0q6ULsw1os76jhZYHGi+2AMyjfmJA2644XRDtFih99xgr03HrxudlXHp6NMwpfga3/ficr2sK2iuhgCbqff30p9OiOWOgk7BVuPtwE9i410fMcrmNKNjsonanAZRLvoxqfbAC0523cA/GTAOVpN1pElDdCKI0FiP6SJ+aaMuFJBERESUnl8nTGvLCv/PPGdC2CyhVayisAcZDsTImV7rt4KBn2HQA24XA+1JyN9+oukL25rK9/2JXyztSFzs5GNWkPBl1+hXjD7SkAHJ6BUbZROVmDbUlu8ui2HU0cl0kyMu4dJioXJOvF6k75FEFU/I7et5rZ/MoOfvf6zZ7F5Wj3urncV3bEhzLU6WL2p12aCOUmt31ugFAfz4s0Azm89SaCfORKS84tlIJNoMXrPOOhq/lr4X6NqAX3XlZ3FrSYG49uLAXjYP91WPeDQ282P00f+mIiIiGNL2C5oaGxloF2GhGl8GjoHi3zX13zhVZV5hewfGuLtSDCsDzxpUO9i67+a0m9KMKKts6mufeAy34/l2gV53qnU1hP+e3nHFP3LrmiyU0y+IGEhj2ugaSHctTpFstYHOpv3tUY3EKFRgwykBjMSxmUOTzrg54LfTjL5VgU7xgOxuanXFS5N+tVtCQAtOpNROQBgoaRvZ63uu6MrRpDXm0YYoneqHuvxCIiIgonFwmn5poi/8Lgk/yTRz6ZtldIXuGTU+JWDewjrGCQ5hii27b9D9UN6U8GXX6FWN3J+0JXjcuqQ6XZN24vIxLT0UWK4+EwGNLg9kWW2XP6bqmntMaClvLaEoBeyyn6+zKVgfGTCM4kFOYhXswrlDX6FSCTZH3DoY0wteDLROaN4KXO3VgzPT6yw9toQQdDdRVwetpDblAF4EoTlcG510SIiIi6oM2B02u8JuHMGeWccttHTh1us/uOt1pvfJbw9yMHByYODzSsHwzm0L66LRebHjdLVUPwbuP295rRqNOv1rs7qL+ANEOtHq95oCW03226euOGr9uXF7GpY/M6QNsHrldZy/uun6adR9uwhS7Py82ADRQiIkrel1ns1h5aEA7qqDQx+uDcrfvsSWPGBSmv9FopVGXVCPSuRSje8WP1hVhV7cgj+R1Yliab4SysP2WKUY2s4TtyftNRER0iZ3raLSB0UPlkTT9/9uj0wrlqlQ/CIz8OmR6KqN2yvUbYfRTm3zM8mijo04fvfMcjVad32HXjJ0XXt7ErRubl3HpISOLihLUb1XfmeAIuyO+rpMcy4hd1tFoLSt4DoJC8jgQo4Tkc+z2z98wo9GOKNj0B3jNsmq4X5d0M7CSfRkjecFgb/LvY8R+KzTL/m0Ft0dERHT5nXew6VWW3fJTqATbZWuwsi8uYweg7vrBytcw6elUynsVyPD6gfPg2pn8P1txHumjda7BpiXnt1BRd+p+gfpkIMBUrOuJy8uo9JDAQZSofit9ZxTLj/66TnAsI3Ylg00hprDT/NdT9LV5sYYJNjOWZVlya6eKYRg4OztDtVqVk4iIiGgM5HI57OzsQNM4hA2l4+DgAOvr69jf35eTiAZ2+/ZtFItFFIt9/c4Ijcjk5CSePHmCiYkJOSlW6u9sEhEREREREY1PsBn42RFpSjrCExEREREREY3c+ASbvqHFFdOjsB9LJSIiIiIiovM2PsEmERERERERjQ0Gm0RERERERJQ6BptERERERESUOgabRERERERElLrEv7NZKpXwqU99CnNzc3ISERERjYGPfexj+OZv/mb81b/6V+UkooGcnZ3hM5/5DN72trfJSUQD+/SnP43Xvva1uHbtmpxEF+CTn/wkPve5zw30O5uJg82f+ImfwGc+8xn8w3/4D+UkIiIiGgPvete78C//5b/EG97wBjmJaCCHh4f4hV/4Bfzcz/2cnEQ0sLt37+JNb3oTbt68KSfRBfihH/ohnJ2djTbYNAwDZ2dnqFarchIRERGNgVwuh52dHWiaJicRDeTg4ADr6+vY39+Xk4gGdvv2bRSLRRSLRTmJLsDk5CSePHkyULDJdzaJiIiIiIgodQw2iYiIiIiIKHUMNomIiIiIiCh1DDaJiIiIiIgodQw2iYiIiIiIKHVDBptd1GYzyMzW0JWT9krIZEpoSbO71RwymYwwBZex182gtCcn2J+XqwY+LYSzf77PcybVPgMAWigp0ltLim14Uw61U2HhSIp9WgrkAJA0r1xOniVaVkmxX+4k5UVwv6KPJZh3cn6Fn9fgZwWPy15G3ia8c+m/jpzz60zBa4yIiNIjlS2KMiKKvwwIu/8Plh4sX+SyIaJcFLd1WkMusK5KC6WIsspdP1gWDpt+lQxzrHHrDpPuT/NNXh0qwTKBulzw89K5bhPsS6JlrqiQGMYzbLpzjoL3CjfP486T6j5yeQ0ZbDqOKigEvpQyO2On1vJoWhYsd9oFCiE358Zi1IlKTt8VPs+yYFkdGKhgSlHodasVNMo69KMK7gr7NL8l7rMOzBjoeNs7xsq0uJUwXdRmp7C51PHvS7sgfXH7y6vWUgaZRfiW7Wy0URjgYkycV+WmtJwzbc0LC9lfkgKkZXfzqFwLHodff3kAmKjcjLv5tVDKFNDecPL/xEB7sf88IiKiZFpLU6jk3TLALu+ClfgQeyVfGdDZaKMglpXDpiO8LKsv9BYJlosWLKuOeTiB5rUK4JYrVgdzd1RllF3+NOTZ6KI2K5RLVhP5tSlh/WHTr5JhjjVu3WHTAUCDcSJfJxasRyvI9rOMr37Zm45Xe1sZ+roFku1LomWumhZKi8Fvas+w6WGce0S5GXueOhtA5Vo6MdJ5SCXY1Ms6zLVCZKW9Wy2gAgMdXwYCWKjD2tWDgeWMDn2mgYIc5KQii5XndWC7Lp2oLh5smdCLdZTKQOPFlD/79AE2jzQs3xS/olmsPDSgHW3igZN/feXVXgmFbR1Nadns6jGaZROVDw57DGF5Fa+15HxpfAGofRydDS0yf/vKAwBwHhBEPvTYq6MBHYZ7w56+heUZE5sPI9YhIqLBnNZQ2dZgPOfexe3yxNx6gC5UvVKcFpmllv33nQa0jXteGZBdNaB7ZeWw6enoPtyEOWOg6QUC9jE27vSCWrt3TwHtso7Ar5vu3UXlSCiXMI97Yvk4bPpVEnOsdj6LdQP7gXeu2o1dd+h0ujJaSwWgrMuzPcOmqzmB5oyBjlxnVsjeXA7eSy6xVIJNFOt2YBPasmQHcdrSLfWTkIV7MGYaqPueEM3h3kMD2nYh4VOrFJw+wOaRjtICMF8cLMCKNK0hD0UAOL2CY691tL+8ar3YAMolf0DmmN+SWxrPUwt1XyXDL7t6HLFv/eWBrYT6bsxDj4W6/2mRMvgnIqJUmIcwkYcm9vzR5ryHq/JDUe8h49Y8ABOHR0D+unh/1jDnPSAcNj1FeS1YVh0dwnT/nrVbqo6fm/MvA6D7uA3MzPkqjtnrea/+MWz6VRJ3rPNbHRgzDVSch87uA+/j1WzsusOm0xWxV0IBTdSLcoJj2HQlIdBM2GLcfbgJCA/SLrt0gk0A81vNiJYl1U1flIWWB9qPpXWnV9Dc0IKtWEOzn3jKQVrrgxWY7jwnoHFvWumYR/3EDqDD+133k1ddmG1Amx3l8w11XsF3DL3JezBwaqItVzIS6ycPBAtxDz1cTh/4axVgo5mwCzQREfVNqqDbD1175p9zHipXayisAcZDfxe+Od/K9r2/Z9h0dVkmd/NtLMplXa/czt5clh6KO2WmYH41pgIpB6ualGfDpl8lkcfqtJyvFVCrOr2+xIfakeumkA4TlWvytSLXXxMsc1TBlHRNBt51HvK6tSXYl0TLXBVd1O60QxtJhk9XsbtnNwDoz4fdJ4LnYGrNhPnIe5x16aUWbALz8S1LA8iuNmEM2Z02+IVz3iGR3i+sbwN60Z2Xxa0lzevuk5rpFRy7/a539d5FJL9HckGS5VWy9wUCAi++p3/Din7o4ZpH3dlf49FU8CZORETnw32ovHZBD/8UZZnv3Tjlu2/COA3TKzje1YWyswA8b1zdYO+ycx86rzWg70qv4Yxc8N06/7t3CZdRvbOZoA7W13ULJNuXRMtcDd1qAZtL4fegYdNVGotTqMCAUY4ap0ZxDnZ1YLuSarw1SikGm1EtSxrmZhQtUR67hU7dkuW80zhEd1rxC9fZ0OwTJz156FYraEjB1tSaCUgDBaVqoe7slx0g2Z/TT17ZT2nTfLqRJK8SmdaQRxum+EXwjtcNtMP0kwey/h56jKS7NBER2cTupHB7vagFyzITh75Z9r2/Z9j0lIhlm3WMFRzClFt0o7RNf53JlPJs2PSrpI9jDdQh4tYdNp3G12kNha1l4d1rybDpYZyusytOF/DAAGZhFu7BGMUrASOSbrAptizdEe/oMa2EzovXpbBWMa87bQGbclqfvHdEfKM4Oe8IeqOMuVMHxkx6AwV1q7mQFkwxuOovr6KCpfDPS0adV0nNozTwAEX95UGA99Cj4qvUDJsfRETUB20OmvzQ0TyEObOMW+7T/1On++yu/FBZ9dDRxKH3nv2w6aPTerER7HIZIns9HwjIu4/b3qsrw6ZfJYmO1R00UXroHLfusOk03roPN2GK3ZcXGwAa3q86DJseptd11h0sNK5nnp+60eUSshKqVqtWpVKR5nYsYwaWvivN3tUtABagW01vpr2sf15vWd82dnULM4bVEWb11oelbfhTwoXsn9W0dKD3GSeGpUGzjBN5OXf/FPsc2L8knM8t+7am+Iw+8sqyrGY5uGxnQ1MuGy5hXrnblo9BKeR4TwxL810f9mf7z2vyPFDvj/PZvmXtefL/ya8nIqLxpmmadXx8LM8emWZZLAPke73/f7vcEu75UtnY2dD8Ze+Q6eqyQxRWLgrk+sOubiGsPiEva1mBPAiWU8Omj97+/r5148YNefYIxB2r//9mWay7xK07bHrT0gPnVpZgmQT1y1Su2yT7kmiZ0SkWi9b9+/fl2ecjUC+XDJsedo4CddyQcxC7/fRNTExYr776qjw7kdEEm+6XXJERbhDUm4LLhH7ZnCAleXAQvn9ewFNuSjckmSIgCdu/RNwgSphCtpUorxz9LKuWLK8s5WdFf659Lfgn/+fIN3EhJfBZwe2H3ngDX1ox0LUn1WcSEV1V5x1sBso84V4drCc4ywrL+MuAkPv/gOmhZYcnolwUOWWNO4Uurww2La+eEV4uDZs+WucXbFoRx+pcO776lFx/C1vXv/xg6SFBgU+CZRLUL9O5bhPsS6JlRuepDDaFOrOd5r/metP5n5dhgs2MZVmW3NqpYhgGzs7OUK1W5SQiIiIaA7lcDjs7O9C0xG8UEkU6ODjA+vo69vf35SSigd2+fRvFYhHFYl+/I0IjMjk5iSdPnmBiYkJOipX6O5tERERERERE4xtsntaQ8/2MhjwNMqjNEAI/6yFNFzkwzWXLKyIiIiIiuvLGN9gUf69SOZ3z7wD5hj5XTI/Cfqz1HFy2vCIiIiIioitvfINNIiIiIiIiurQYbBIREREREVHqGGwSERERERFR6hL/9Ml73/tefOpTn8J3fMd3yElEREQ0Bn7xF38Ri4uL+Oqv/mo5iWggn/vc57C/v49/8A/+gZxENLBf/dVfxRve8AZ84zd+o5xEF2Brawuf//znB/rpk8TB5r/4F/8Cv/Vbv4WbN2/KSURERDQG3v/+9+O9730vvv7rv15OIhrIo0ePcP/+fayvr8tJRAP7qZ/6KbzxjW/Ed3/3d8tJdAFWVlbwR3/0R6MNNg3DwNnZGarVqpxEREREYyCXy2FnZweapslJRAM5ODjA+vo69vf35SSigd2+fRvFYhHFYlFOogswOTmJJ0+eDBRs8p1NIiIiIiIiSh2DTSIiIiIiIkodg00iIiIiIiJKHYNNIiIiIiIiSh2DTSIiIiIiIkrdkMFmF7XZDDIZxTRbQ1deHADQQkmR3lpSbMObcqidCguHcratmpZawnIR+53JoLQnLBq6fAniFt1lclX5qBXr+vZFWEaVZ3ulwGd1qzlhe0nzhoiI6CL4y+ZgORlFKkPDys/QdJe9XGz5Hlh/3NOvostwPanrZz2juN6kNN/k7kdEPdirYyZYZq8UTHOm/vJ7DIQeq1i/lvIscN4EkdcFvG2F56OcHn7eg9fX5TRksGnTdy1Yljh1YKCCKcXJ6FYraJR16EcV3BUyaX5LWH9XB2YMdLztHWNlWtxKFA3Gibw/TejbhcCJDe63PdUXhIVOa8hlprC51PEt09looxAX6DnrVvJN37pNFNQX4lEFhdCLz9at5jC1lkfTy6s8KtcU2yIiIrpwXdRmC2hvuGVoE/m1qcSVpNaSWIZ2YLT9ZXlcuq2L2uwUKkfS7ATrj3v61XMZrifYAcFiQ57pGN31htC6ax3z3hKqerAF69EKsv0s46uH96bj1d5WroSFunSMHRgzAMqGE3vY11yjHH1ebFHXhWSroLhunc+SZ6vO+66OxmJMHHJZWAlVq1WrUqlIczuWMQNL35VmW5Zl7eoWoFtN38ze8s0yLJT9qZ5d3cKMYXXk+bGalg7NMk7k+ZbV2dCEbUbst4+9nLah3pNmWUyTl5X/9/Mfv7M/Zd2CvP++fFRtM+mxEBHR007TNOv4+FiePTqKukBnQ/PKv2YZUnrT0uGUcyeGpanKRLcsj0t3PwuwUNYtHVJZGbf+uKefk/39fevGjRvy7NG44OvJ1Sy7dTbFvozqektU3wuvB/ckWCbkuM9TsVi07t+/L88eOfscBq8hMd/Fa04Udl34OdvbMCxN3saJYWkzuqUr4ongeU9wHlM0MTFhvfrqq/LsRFJp2Uzs9AE2j3SUFoD5og5s1y93i5yzv0bIU5z5rYgnPKcPsHmkYfmmOn3+OQOafPzFOpplE5Wbiu60AIAsVh7Jn2niUPH0jIiI6KJ1H7eBmTlowrzs9bxX/s9vdWDMNFBxWglaS3YLwvFqFjAPYSIPTezZpM1BO9rEg1PEpwMAlu2eQFslYSFH3Prjnn4FXfz1ZHeTLKCJelGY5xnh9UbnoIW7ayb0XX9L8dwM0HjRrbF38WDLhDYrXoVx14XC9VtYbvvjgO7DTWCphDlhXqi9Ohpe6+vlNqJgs4vanQZQLgknC2h9sALTnbdwz3dDGC374tGWbgldCIDGYrD/s697q3kIU7qpJaa6aYimNeTRhindQOa3mtATdKd1dasVNGYM3BO7/hIREV0Wec1X9kITy9UsVp7XYa4VUKuWUNjW0dwSag5yGTytIS/+H5OeXV3x1UMCYtYf+/Sr6AKvJ7t+24bxnPqqGvn1pqy7yl0pTVSuycvIr1slWOaogim5jqx4Pe4qUdep7YYe+xW4DDKZKRw+Lzf8RF8XalncWmqj7nWl7eLBFrB8Ux11BM77YgNomyGNU5dLKsFmIAPc9xTFLzhaqG8DetGdl8WtJQ3m1oOUM0r1BbL798utkIH+z5Yl9XsP8g/OEzKoz1DmUd91bpQxT7Ls9zcB46HYD5+IiGiMLDi9etYaUosC0QBGeD11qwVsLjUvtDUpWHeVxzVRvY8p50OCZVTvbPrq9VeN02IpNUy5A/ZUZt33hDuYu+MPvAe9LrI3l9F2W0xPH2ATy7gVso3AeT8xoEnj31xWqQSbYgZ0NjT7Ipai+261goYUmE6tmUDqGeX/AjXLABDeFTaSNgft6BCmMCu7eiwdawhtDpqi5dJzaqId1vLp3ihDu9MKgeaJfJMhIiK6ROSn76a/XBW1H0ulnlQG22WnIC49Ttz6455+FV3U9XRaQ2FrGc1B6pOuqO0jQTqNTtjrb3t1NHxxRBYrDw1o2xW7UWiY62L6Fpbb9nbsLrRyoBthegVGWezee3mlEmyKsqvHdqDkGyHVeVrgjR7mTvaIT6PMKLf/fmGQFsjpW1getKvv9C0sz5jYfKhe19elWMHrTnsneJtpLWWcEWkZaBIR0eWVvZ4PVKC7j9v+12z2nO6Ocq8e1UNb8xDmjPP0Py49Ttz6455+BV3k9dR9uAlT7Fq62ADQiP9lAlfM9mPTaaS6DzcHyuvhrossbi0Bmw9bThfaxKGmJ/Du6GUkjxgUpr/RaO2RliJH2HIpRhYbfBSskJGZTgxLg2L018B+KzjrBkaV3dXtEcekUcJ8ywU+1xYcLS1kf9zPEJYNjpJFRESUzLmPRhsoG+WRHf3/N8tiuaoeuV0sZ+PSe+TPdebGrD/u6efhXEejDRyjfF7P63oKqb965P1y5sZsPzo9pK7oE1IP9kmwzMD18PSc92i0/rz3pfRGNLYs7zyol427LqzgteHGCqHxRMh5j4qtRmCY0WhHFGz6Ay35y+4nn8RhLvKIL5ATuNmf41woCJkCF5Bqeflz5IvDPz/J9lX52CyLwaYTxCum4OcSERH5nX+waQXKLrkS5S/v5TqBVIaGlJ/h6S515T9+/XFPH73zDTatS3I9xQUVo7jewuuKPRH1YE+CZQauh6fnfINNO2/D69JS/TsqbyKvC0txbcifrf7f+2xhir4W0jVMsJmxLMuSWztVDMPA2dkZqtWqnERERERjIJfLYWdnB5o2Bl2vaCwcHBxgfX0d+/v7chLRwG7fvo1isYhiMelvidAoTU5O4smTJ5iYmJCTYqX+ziYRERERERHR+ASbeyXp50ykaZABgIiIiIiIiGgkxifYXKhLI9lK0yP+1iQREREREdFlMT7BJhEREREREY0NBptERERERESUOgabRERERERElLrEP31y584dfPrTn8bb3/52OYmIiIjGwAsvvIB/9I/+Ef7G3/gbchLRQDqdDvb29vBjP/ZjchLRwLa3t5HL5fCmN71JTqILsL6+jj/6oz8a6KdPEgebq6ur+NSnPoW3vvWtchIRERGNgZ/5mZ/BD/7gD+J1r3udnEQ0kD/4gz/Axz72Mfzoj/6onEQ0sF/+5V/GtWvX8M3f/M1yEl2An/mZn8EXv/jF0QabhmHg7OwM1WpVTiIiIqIxkMvlsLOzA03T5CSigRwcHGB9fR37+/tyEtHAbt++jWKxiGKxKCfRBZicnMSTJ08GCjb5ziYRERERERGljsEmERERERERpY7BJhEREREREaWOwSYRERERERGljsEmERERERERpW7IYLOL2mwGmUxwKu05i5zWkFOke9NSy7/Fas6fPltDV1zA2Z63fVlcOhEREZ2jFkpCuZ6r+kr1GFI9Q6ozxKcL9krIZEpQL2FvJ1h3SLr9QdcfdfrV5K8rhp3TEHslYd0caqd9pnvsvA+9nvdKwTqsOz/J9gPrh9e5vfPu27Z/cvczUM8WJvv6jficfvN6rPnzIXie/fe16O9e2P3B3Yabr9I2vSniOhkDQwabNn3XgmUJ066OxqKTMdMrOPbSOjBmpOW35p2t2CdiamsZHWFbzXwFU4GLW0P7jirjWyhdq8CUZxMREdEF6KI2W0B7o+OU603k16YUlS611tIUKvlmrw7RLvgqfXHpPS2UFhvyTEcXtdkpVI7k+Um3P/j6o06/kvZKmFrLo+nUEzsbbRRUQZ3KaQ25xTaME7e+mkflmlDHjEsXdKsF5TkHnKBPdb0l3b5y/SxWHgn1Z8uubwMajOfcujSAGcNXj3an49Vsb5mye834p/pCb5FA3d6yYFl1CJ90hTnfae+7Jd+3WihlxPvaIN89exuNclPKV613fThTZwPq62RcWAlVq1WrUqlIczuWMQNL35VmW01Lh2YZJ/L8sOUtq7OhWZgxrI6c4KyjbTgpJ4alQbOMDb03z7WrWyjrlg71ZxARET3NNE2zjo+P5dmjs6tbgG41hVmdDc1C2Z7TLENKb1o6nDLfLe/FusSu3qsrxKULmmVYejlkXwB13SHB9odaf9Tp52R/f9+6ceOGPHtEpDqhZfnrnCeGpbnXj7vGhuad92YZ3rXnpPrqpXHpnhPD0mZ0S1fuCyxAs/RysF4bv/*****************************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;" vertex="1" parent="1">
          <mxGeometry x="90" y="390" width="551.04" height="240" as="geometry" />
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-12" value="" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;" vertex="1" parent="1">
          <mxGeometry x="680" y="380" width="608.42" height="430" as="geometry" />
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-14" value="0x000000000" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="630" y="50" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-16" value="" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=default;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/png,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;" vertex="1" parent="1">
          <mxGeometry x="1320" y="200" width="953" height="586" as="geometry" />
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-17" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="m3XBTr2kCk1S9m1vjy7l-6" target="m3XBTr2kCk1S9m1vjy7l-19">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-18" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="180" y="100" width="260" height="180" as="geometry" />
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-2" value="R5F View" style="swimlane;whiteSpace=wrap;html=1;" vertex="1" parent="m3XBTr2kCk1S9m1vjy7l-18">
          <mxGeometry x="100" width="160" height="180" as="geometry" />
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-3" value="RAT" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="m3XBTr2kCk1S9m1vjy7l-2">
          <mxGeometry y="60" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-5" value="Tightly coupled&lt;div&gt;memory&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="m3XBTr2kCk1S9m1vjy7l-2">
          <mxGeometry y="100" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-6" value="RAT" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="m3XBTr2kCk1S9m1vjy7l-2">
          <mxGeometry y="140" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-4" value="Non RAT" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="m3XBTr2kCk1S9m1vjy7l-2">
          <mxGeometry y="20" width="160" height="40" as="geometry" />
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-13" value="0x000000000" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="m3XBTr2kCk1S9m1vjy7l-18">
          <mxGeometry y="10" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-15" value="0x000007FFF" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="m3XBTr2kCk1S9m1vjy7l-18">
          <mxGeometry y="40" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-20" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="m3XBTr2kCk1S9m1vjy7l-19" target="m3XBTr2kCk1S9m1vjy7l-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="m3XBTr2kCk1S9m1vjy7l-19" value="RAT&lt;div&gt;Module&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="530" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="bpUn21p71q_MDf-lG9of-2" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="m3XBTr2kCk1S9m1vjy7l-6" target="tkaZd_RN_GHhowruxK_n-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="bpUn21p71q_MDf-lG9of-5" value="" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="m3XBTr2kCk1S9m1vjy7l-3" target="bpUn21p71q_MDf-lG9of-4">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="440" y="176" as="sourcePoint" />
            <mxPoint x="720" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="bpUn21p71q_MDf-lG9of-4" value="RAT&lt;div&gt;Module&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="530" y="140" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
